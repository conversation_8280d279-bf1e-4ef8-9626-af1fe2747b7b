apiVersion: iam.aws.upbound.io/v1beta1
kind: Policy
metadata:
  name: emr-on-eks-policy
spec:
  forProvider:
    policy: |
      {
          "Version": "2012-10-17",
          "Statement": [
              {
                  "Effect": "Allow",
                  "Action": [
                      "iam:CreateServiceLinkedRole"
                  ],
                  "Resource": "*",
                  "Condition": {
                      "StringLike": {
                          "iam:AWSServiceName": "emr-containers.amazonaws.com"
                      }
                  }
              },
              {
                  "Effect": "Allow",
                  "Action": [
                      "emr-containers:CreateVirtualCluster",
                      "emr-containers:ListVirtualClusters",
                      "emr-containers:DescribeVirtualCluster",
                      "emr-containers:DeleteVirtualCluster",
                      "emr-containers:StartJobRun",
                      "emr-containers:ListJobRuns",
                      "emr-containers:DescribeJobRun",
                      "emr-containers:CancelJobRun",
                      "emr-containers:DescribeJobRun",
                      "elasticmapreduce:CreatePersistentAppUI",
                      "elasticmapreduce:DescribePersistentAppUI",
                      "elasticmapreduce:GetPersistentAppUIPresignedURL"
                  ],
                  "Resource": "*"
              },
              {
                  "Effect": "Allow",
                  "Action": [
                      "eks:CreateAccessEntry"
                  ],
                  "Resource": "arn:*:eks:us-east-1:093949242303:cluster/staging_eks_data1"
              },
              {
                  "Effect": "Allow",
                  "Action": [
                      "eks:DescribeAccessEntry",
                      "eks:DeleteAccessEntry",
                      "eks:ListAssociatedAccessPolicies",
                      "eks:AssociateAccessPolicy",
                      "eks:DisassociateAccessPolicy"
                  ],
                  "Resource": "arn:*:eks:us-east-1:093949242303:access-entry/staging_eks_data1/role/093949242303/AWSServiceRoleForAmazonEMRContainers/*"
              },
              {
                  "Effect": "Allow",
                  "Action": [
                      "s3:GetObject",
                      "s3:ListBucket",
                      "s3:PutObject"
                  ],
                  "Resource": "*"
              },
              {
                  "Effect": "Allow",
                  "Action": [
                      "logs:Get*",
                      "logs:DescribeLogGroups",
                      "logs:DescribeLogStreams",
                      "logs:PutLogEvents",
                      "logs:CreateLogStream"
                  ],
                  "Resource": "*"
              }
          ]
      }
  providerConfigRef:
    name: provider-config-aws
