apiVersion: iam.aws.upbound.io/v1beta1
kind: Role
metadata:
  name: emr-on-eks-execution-role
spec:
  forProvider:
    assumeRolePolicy: |
      {
        "Version": "2012-10-17",
        "Statement": [
          {
            "Sid": "",
            "Effect": "Allow",
            "Principal": {
              "Federated": "federated-to-set"
            },
            "Action": "sts:AssumeRoleWithWebIdentity",
            "Condition": {
              "StringEquals": {
                "condition-string"
              }
            }
          }
        ]
      }
  providerConfigRef:
    name: provider-config-aws
