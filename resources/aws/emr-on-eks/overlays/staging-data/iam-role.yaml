apiVersion: iam.aws.upbound.io/v1beta1
kind: Role
metadata:
  name: emr-on-eks-execution-role
spec:
  forProvider:
    assumeRolePolicy: |
      {
          "Version": "2012-10-17",
          "Statement": [
              {
                  "Effect": "Allow",
                  "Principal": {
                      "Federated": "arn:aws:iam::************:oidc-provider/oidc.eks.us-east-1.amazonaws.com/id/18382A10391BAA80C09455AFBE97F91D"
                  },
                  "Action": "sts:AssumeRoleWithWebIdentity",
                  "Condition": {
                      "StringLike": {
                          "oidc.eks.us-east-1.amazonaws.com/id/18382A10391BAA80C09455AFBE97F91D:sub": "system:serviceaccount:emr-eks-test:emr-containers-sa-*-*-************-*"
                      }
                  }
              }
          ]
      }
