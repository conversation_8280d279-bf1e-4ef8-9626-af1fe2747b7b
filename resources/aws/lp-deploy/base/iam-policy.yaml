apiVersion: iam.aws.crossplane.io/v1beta1
kind: Policy
metadata:
  name: lp-deploy-policy
spec:
  forProvider:
    name: lp-deploy-policy
    document: |
      {
        "Version": "2012-10-17",
        "Statement": [
          {
            "Effect": "Allow",
            "Action": [
              "s3:GetAccessPoint",
              "s3:PutAccountPublicAccessBlock",
              "s3:GetAccountPublicAccessBlock",
              "s3:ListAllMyBuckets",
              "s3:ListAccessPoints",
              "s3:ListJobs"
            ],
            "Resource": "*"
          },
          {
            "Effect": "Allow",
            "Action": "s3:*",
            "Resource": [
              "arn:aws:s3:::lp-deploy-artifacts",
              "arn:aws:s3:::lp-deploy-artifacts/*"
            ]
          },
          {
            "Effect": "Allow",
            "Action": "sqs:*",
            "Resource": [
              "arn:aws:sqs:us-east-1:*:release-notifications*",
              "arn:aws:sqs:us-east-1:*:e2e-notifications*"
            ]
          }
        ]
      }
  providerConfigRef:
    name: provider-config-aws
