apiVersion: iam.aws.upbound.io/v1beta1
kind: Policy
metadata:
  name: starrocks-access-policy
  namespace: starrocks
spec:
  forProvider:
    policy: |
      {
          "Version": "2012-10-17",
          "Statement": [
            {
                  "Effect": "Allow",
                  "Action": [
                      "s3:GetObject",
                      "s3:PutObject",
                      "s3:DeleteObject",
                      "s3:ListBucket",
                      "s3:HeadObject"
                  ],
                  "Resource": [
                      "arn:aws:s3:::lp-datalakehouse-production",
                      "arn:aws:s3:::lp-datalakehouse-production/*",
                      "arn:aws:s3:::lp-datawarehouse-production",
                      "arn:aws:s3:::lp-datawarehouse-production/*"
                  ]
              }
          ]
      }
  providerConfigRef:
    name: provider-config-aws
