apiVersion: iam.aws.upbound.io/v1beta1
kind: Role
metadata:
  namespace: starrocks
  name: starrocks-access
  annotations:
    crossplane.io/external-name: starrocks-access
spec:
  forProvider:
    assumeRolePolicy: |-
      {
        "Version": "2012-10-17",
        "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
              "Federated": "arn:aws:iam::************:oidc-provider/oidc.eks.us-east-1.amazonaws.com/id/2C8B96F9570D9DE958C717D6477E797A"
            },
            "Action": "sts:AssumeRoleWithWebIdentity",
            "Condition": {
              "StringEquals": {
                "oidc.eks.us-east-1.amazonaws.com/id/2C8B96F9570D9DE958C717D6477E797A:sub": "system:serviceaccount:starrocks:starrocks"
              }
            }
          }
        ]
      }
  providerConfigRef:
    name: provider-config-aws
