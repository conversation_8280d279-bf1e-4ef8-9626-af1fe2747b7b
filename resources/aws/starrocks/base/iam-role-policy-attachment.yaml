apiVersion: iam.aws.upbound.io/v1beta1
kind: RolePolicyAttachment
metadata:
  name: starrocks-access-role-policy-attachment
  namespace: starrocks
spec:
  forProvider:
    policyArnRef:
      name: starrocks-access-policy
    roleRef:
      name: starrocks-access
  providerConfigRef:
    name: provider-config-aws

---
apiVersion: iam.aws.upbound.io/v1beta1
kind: RolePolicyAttachment
metadata:
  name: starrocks-access-role-policy-attachment-service-glue
spec:
  forProvider:
    policyArn: arn:aws:iam::aws:policy/service-role/AWSGlueServiceRole
    roleRef:
      name: starrocks-access
  providerConfigRef:
    name: provider-config-aws

---
apiVersion: iam.aws.upbound.io/v1beta1
kind: RolePolicyAttachment
metadata:
  name: starrocks-access-role-policy-attachment-glue
spec:
  forProvider:
    policyArn: arn:aws:iam::aws:policy/AWSGlueConsoleFullAccess
    roleRef:
      name: starrocks-access
  providerConfigRef:
    name: provider-config-aws
