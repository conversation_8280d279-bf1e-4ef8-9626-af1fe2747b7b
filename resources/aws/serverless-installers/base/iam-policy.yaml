apiVersion: iam.aws.upbound.io/v1beta1
kind: Policy
metadata:
  name: serverless-installers-policy
spec:
  forProvider:
    policy: |
      {
        "Version": "2012-10-17",
        "Statement": [
          {
            "Effect": "Allow",
            "Action": [
              "cloudformation:*",
              "sqs:*",
              "sns:*",
              "serverless:*",
              "logs:*",
              "iam:*",
              "statemachine:*",
              "states:*",
              "events:*",
              "s3:*",
              "ec2:*",
              "ssm:*",
              "lambda:*",
              "apigateway:*",
              "scheduler:*",
              "cloudwatch:*",
              "dynamodb:*"
            ],
            "Resource": "*"
          },
          {
            "Effect": "Allow",
            "Action": [
              "secretsmanager:GetSecretValue"
            ],
            "Resource": [
              "arn:aws:secretsmanager:*:*:secret:lambda-kafka-*"
            ]
          }
        ]
      }
  providerConfigRef:
    name: provider-config-aws
