apiVersion: iam.aws.upbound.io/v1beta1
kind: Policy
metadata:
  name: kubecost-ec2
spec:
  forProvider:
    policy: |
      {
          "Version": "2012-10-17",
          "Statement": [
              {
                  "Sid": "kubecostEC2Access",
                  "Effect": "Allow",
                  "Action": [
                      "ec2:DescribeInstances",
                      "ec2:DescribeInstanceTypes",
                      "ec2:DescribeRegions",
                      "ec2:DescribeAvailabilityZones",
                      "ec2:DescribeSpotPriceHistory",
                      "ec2:DescribeReservedInstances",
                      "ec2:DescribeReservedInstancesOfferings",
                      "ec2:GetReservedInstancesExchangeQuote",
                      "ec2:DescribeInstanceAttribute",
                      "ec2:DescribeVolumes",
                      "ec2:DescribeVolumeTypes",
                      "ec2:DescribeSnapshots",
                      "ec2:DescribeImages",
                      "ec2:DescribeAddresses",
                      "ec2:DescribeNetworkInterfaces",
                      "ec2:DescribeSecurityGroups",
                      "ec2:DescribeSubnets",
                      "ec2:DescribeVpcs",
                      "ec2:DescribeTags"
                  ],
                  "Resource": "*"
              },
              {
                  "Sid": "kubecostEKSAccess",
                  "Effect": "Allow",
                  "Action": [
                      "eks:DescribeCluster",
                      "eks:ListClusters",
                      "eks:DescribeNodegroup",
                      "eks:ListNodegroups"
                  ],
                  "Resource": "*"
              },
              {
                  "Sid": "kubecostELBAccess",
                  "Effect": "Allow",
                  "Action": [
                      "elasticloadbalancing:DescribeLoadBalancers",
                      "elasticloadbalancing:DescribeTargetGroups",
                      "elasticloadbalancing:DescribeListeners",
                      "elasticloadbalancing:DescribeRules",
                      "elasticloadbalancing:DescribeTags"
                  ],
                  "Resource": "*"
              },
              {
                  "Sid": "kubecostRDSAccess",
                  "Effect": "Allow",
                  "Action": [
                      "rds:DescribeDBInstances",
                      "rds:DescribeDBClusters",
                      "rds:DescribeReservedDBInstances",
                      "rds:DescribeReservedDBInstancesOfferings",
                      "rds:ListTagsForResource"
                  ],
                  "Resource": "*"
              },
              {
                  "Sid": "kubecostCloudWatchAccess",
                  "Effect": "Allow",
                  "Action": [
                      "cloudwatch:GetMetricStatistics",
                      "cloudwatch:ListMetrics",
                      "cloudwatch:GetMetricData"
                  ],
                  "Resource": "*"
              },
              {
                  "Sid": "kubecostPricingAccess",
                  "Effect": "Allow",
                  "Action": [
                      "pricing:GetProducts",
                      "pricing:GetAttributeValues",
                      "pricing:DescribeServices"
                  ],
                  "Resource": "*"
              }
          ]
      }
  providerConfigRef:
    name: provider-config-aws
