apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  namespace: atlantis
  name: atlantis-ingress
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:172158540696:certificate/0ecefba3-1a3b-4282-ae7e-95f527a28685
    alb.ingress.kubernetes.io/group.name: ingress-global
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
spec:
  rules:
  - host: atlantis-public.ops.luxurycoders.com
    http:
      paths:
      - path: /events
        pathType: Prefix
        backend:
          service:
            name: atlantis
            port:
              number: 80
