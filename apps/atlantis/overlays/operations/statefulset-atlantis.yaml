apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    app: atlantis
    chart: atlantis-5.1.3
    helm.sh/chart: atlantis-5.1.3
    heritage: Helm
    release: atlantis
  name: atlantis
  namespace: atlantis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: atlantis
      release: atlantis
  serviceName: atlantis
  template:
    metadata:
      annotations:
        checksum/config: 01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b
        checksum/repo-config: 01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b
        vault.security.banzaicloud.io/vault-addr: "https://vault.ops.luxurycoders.com"
        vault.security.banzaicloud.io/vault-role: "atlantis"
      labels:
        app: atlantis
        release: atlantis
    spec:
      imagePullSecrets:
        - name: image-pull-secret
      automountServiceAccountToken: true
      containers:
      - args:
        - server
        env:
        - name: ATLANTIS_DATA_DIR
          value: /atlantis-data
        - name: ATLANTIS_REPO_ALLOWLIST
          value: github.com/luxurypresence/infrastructure
        - name: ATLANTIS_PORT
          value: '4141'
        - name: ATLANTIS_ATLANTIS_URL
          value: https://atlantis.ops.luxurycoders.com
        - name: ATLANTIS_GH_USER
          value: luxurypresenceci
        - name: ATLANTIS_GH_TOKEN
          value: vault:secret/data/infrastructure/atlantis#GH_TOKEN
        - name: ATLANTIS_GH_WEBHOOK_SECRET
          value: vault:secret/data/infrastructure/atlantis#GH_WEBHOOK_SECRET
        - name: TF_VAR_confluent_cloud_api_key
          value: vault:secret/data/infrastructure/confluent-cloud#cloud_api_key
        - name: TF_VAR_confluent_cloud_api_secret
          value: vault:secret/data/infrastructure/confluent-cloud#cloud_api_secret
        - name: TF_VAR_WUNDERGRAPH
          value: vault:secret/data/infrastructure/atlantis#WUNDERGRAPH
        image: luxurypresence/atlantis:master-20-888fc16
        imagePullPolicy: Always
        livenessProbe:
          failureThreshold: 5
          httpGet:
            path: /healthz
            port: 4141
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 60
          successThreshold: 1
          timeoutSeconds: 5
        name: atlantis
        ports:
        - containerPort: 4141
          name: atlantis
        readinessProbe:
          failureThreshold: 5
          httpGet:
            path: /healthz
            port: 4141
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 60
          successThreshold: 1
          timeoutSeconds: 5
        resources: {}
        volumeMounts:
        - mountPath: /atlantis-data
          name: atlantis-data
        - name: ssh-keys
          mountPath: /home/<USER>/.ssh
      hostNetwork: false
      securityContext:
        fsGroup: 1000
        fsGroupChangePolicy: OnRootMismatch
        runAsUser: 100
      serviceAccountName: atlantis
      shareProcessNamespace: false
      volumes:
      - name: atlantis-data
        persistentVolumeClaim:
          claimName: atlantis-data
      - name: ssh-keys
        secret:
          secretName: ssh-keys
          defaultMode: 0400
