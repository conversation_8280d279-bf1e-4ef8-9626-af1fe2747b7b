apiVersion: v1
kind: Pod
metadata:
  annotations:
    helm.sh/hook: test
  name: atlantis-ui-test
  namespace: atlantis
spec:
  containers:
  - command:
    - /usr/local/bin/bats
    - /tests/
    image: bats/bats:1.9.0
    name: atlantis-ui-test
    volumeMounts:
    - mountPath: /tests
      name: tests
      readOnly: true
  restartPolicy: Never
  volumes:
  - configMap:
      name: atlantis-tests
    name: tests
