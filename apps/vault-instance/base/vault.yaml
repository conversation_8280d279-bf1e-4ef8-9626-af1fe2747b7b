apiVersion: vault.banzaicloud.com/v1alpha1
kind: Vault
metadata:
  name: vault
spec:
  size: 5
  image: hashicorp/vault:1.14.1

  # Common annotations for all created resources
  annotations:
    common/annotation: "true"

  # Vault Pods , Services and TLS Secret annotations
  vaultAnnotations:
    type/instance: "vault"
    monitoring.luxurycoders.com/group: "swat"
    monitoring.luxurycoders.com/monitor-type: "standard"

  # Vault Configurer Pods and Services annotations
  vaultConfigurerAnnotations:
    type/instance: "vaultconfigurer"

  # Vault Pods , Services and TLS Secret labels
  vaultLabels:
    metrics-scrape: "true"

  # Vault Configurer Pods and Services labels
  vaultConfigurerLabels:
    example.com/log-format: "string"

  resources:
    vault:
      limits:
        memory: "6600Mi"
        cpu: "2"
      requests:
        memory: "5800Mi"
        cpu: "1"

  # Needed by backup
  veleroEnabled: true

  # Specify the ServiceAccount where the Vault Pod and the Bank-Vaults configurer/unsealer is running
  serviceAccount: vault

  # Specify the Service's type where the Vault Service is exposed
  # Please note that some Ingress controllers like https://github.com/kubernetes/ingress-gce
  # forces you to expose your Service on a NodePort
  serviceType: ClusterIP

  ingress:
    annotations:
      kubernetes.io/ingress.class: alb
      alb.ingress.kubernetes.io/scheme: internal
      alb.ingress.kubernetes.io/target-type: ip
      alb.ingress.kubernetes.io/healthcheck-path: /v1/sys/health
      alb.ingress.kubernetes.io/healthcheck-protocol: HTTPS
      alb.ingress.kubernetes.io/backend-protocol: HTTPS
      alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:************:certificate/0ecefba3-1a3b-4282-ae7e-95f527a28685
      alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}]'
      alb.ingress.kubernetes.io/subnets: subnet-0f9760f1a317a352a, subnet-0b8f6b68b44c2029a, subnet-051994cea68deee62, subnet-0dd418a8fc762c567 # Transit subnets
    spec:
      rules:
        - host: vault.ops.luxurycoders.com
          http:
            paths:
              - path: /
                pathType: Prefix
                backend:
                  service:
                    name: vault
                    port:
                      number: 8200

  # Use local disk to store Vault file data, see config section.
  volumeClaimTemplates:
    - metadata:
        name: vault-raft
      spec:
        accessModes:
          - ReadWriteOnce
        volumeMode: Filesystem
        resources:
          requests:
            storage: 5Gi

  volumeMounts:
    - name: vault-raft
      mountPath: /vault/file

  # Support for distributing the generated CA certificate Secret to other namespaces.
  # Define a list of namespaces or use ["*"] for all namespaces.
  caNamespaces:
    - "vswh"
    - "vault-backup"

  # Describe where you would like to store the Vault unseal keys and root token.
  unsealConfig:
    options:
      # The preFlightChecks flag enables unseal and root token storage tests
      # This is true by default
      preFlightChecks: true
    kubernetes:
      secretNamespace: vault

  # A YAML representation of a final vault config file.
  # See https://www.vaultproject.io/docs/configuration/ for more information.
  config:
    storage:
      raft:
        path: "/vault/file"
    listener:
      tcp:
        address: "0.0.0.0:8200"
        # Uncommenting the following line and deleting tls_cert_file and tls_key_file disables TLS
        # tls_disable: true
        tls_cert_file: /vault/tls/server.crt
        tls_key_file: /vault/tls/server.key
    telemetry:
      prometheus_retention_time: "1h"
      disable_hostname: true
    default_lease_ttl: 168h
    max_lease_ttl: 87600h
    api_addr: https://vault.vault:8200
    cluster_addr: "https://${.Env.POD_NAME}:8201"
    ui: true

  # Vault operator raft snapshot fail to handle redirection to the active node
  # Issue: https://github.com/hashicorp/vault/issues/15258
  serviceRegistrationEnabled: true

  # See: https://banzaicloud.com/docs/bank-vaults/cli-tool/#example-external-vault-configuration
  # The repository also contains a lot examples in the deploy/ and operator/deploy directories.
  externalConfig:
    policies:
      - name: allow_read_secrets_development_all
        rules: path "secret/data/development/*" {
          capabilities = ["read", "list"]
          }
      - name: allow_db_staging_core
        rules: path "database/creds/postgres-staging-core-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

          path "database/creds/postgres-staging-core-short-for-test" {
            capabilities = ["create", "read", "update", "list", "delete"]
          }
      - name: allow_db_staging_network
        rules: path "database/creds/postgres-staging-network-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }
      - name: allow_db_production_network
        rules: path "database/creds/postgres-production-network-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }
      - name: allow_db_staging_property
        rules: path "database/creds/postgres-staging-property-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }
      - name: allow_db_production_property
        rules: path "database/creds/postgres-production-property-long-life*" {
          capabilities = ["read", "list"]
          }
      - name: allow_db_staging_slide
        rules: path "database/creds/postgres-staging-slide-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }
      - name: allow_db_staging_website
        rules: path "database/creds/postgres-staging-website-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }
      - name: allow_db_production_website
        rules: path "database/creds/postgres-production-website-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }
      - name: allow_db_staging_ai_agents
        rules: path "database/creds/postgres-staging-ai-agents-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }
      - name: allow_db_staging_crew_ai_seo_automation
        rules: path "database/creds/postgres-staging-crew-ai-seo-automation-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }
      - name: allow_db_staging_crm
        rules: path "database/creds/postgres-staging-crm-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }
      - name: allow_db_production_crm
        rules: path "database/creds/postgres-production-crm-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }
      - name: allow_db_staging_ops_service
        rules: path "database/creds/postgres-staging-ops-service-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }
      - name: allow_db_production_ops_service
        rules: path "database/creds/postgres-production-ops-service-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }
      # policy used to generate the token to allow prometheus to grab vault's metrics
      - name: allow_read_metrics_prometheus
        rules: path "/sys/metrics*" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_operations_prometheus
        rules: path "secret/data/operations/prometheus" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_operations_n8n
        rules: path "secret/data/operations/n8n" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_staging_buyer-seller
        rules: path "secret/data/staging/buyer-seller" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_buyer-seller
        rules: path "secret/data/production/buyer-seller" {
          capabilities = ["read", "list"]
          }
      - name: allow_manage_secrets_all_buyer-seller
        rules: path "secret/data/+/buyer-seller" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }
      - name: allow_read_secrets_staging_liveness_checker
        rules: path "secret/data/staging/liveness-checker-lambda" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_liveness_checker
        rules: path "secret/data/production/liveness-checker-lambda" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_staging_api-gateway
        rules: path "secret/data/staging/api-gateway" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_staging_media-lambda
        rules: path "secret/data/staging/media-lambda" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_staging_polaris-firechief
        rules: path "secret/data/staging/polaris-firechief" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_api-gateway
        rules: path "secret/data/production/api-gateway" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_media-lambda
        rules: path "secret/data/production/media-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_public-api-gateway
        rules: path "secret/data/staging/public-api-gateway" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_network-service
        rules: path "secret/data/staging/network-service" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_production_network-service
        rules: path "secret/data/production/network-service" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_mls-data-etl-lookupapi
        rules: path "secret/data/staging/mls-data-etl-lookupapi" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_production_mls-data-etl-lookupapi
        rules: path "secret/data/production/mls-data-etl-lookupapi" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_mls-data-etl-aitriage
        rules: path "secret/data/staging/mls-data-etl-aitriage" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_production_mls-data-etl-aitriage
        rules: path "secret/data/production/mls-data-etl-aitriage" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_cmt-web-scraper-lambda
        rules: path "secret/data/staging/cmt-web-scraper-lambda" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_cmt-web-scraper-lambda
        rules: path "secret/data/production/cmt-web-scraper-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_production_public-api-gateway
        rules: path "secret/data/production/public-api-gateway" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_website-status-service
        rules: path "secret/data/staging/website-status-service" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_website-status-service
        rules: path "secret/data/production/website-status-service" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_realogy-lead-router-lambda
        rules: path "secret/data/staging/realogy-lead-router-lambda" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_realogy-lead-router-lambda
        rules: path "secret/data/production/realogy-lead-router-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_webhook-lambda-lambda
        rules: path "secret/data/staging/webhook-lambda" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_webhook-lambda-lambda
        rules: path "secret/data/production/webhook-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_followupboss-lambda
        rules: path "secret/data/staging/followupboss-lambda" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_followupboss-lambda
        rules: path "secret/data/production/followupboss-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_sendgrid-contacts-lambda
        rules: path "secret/data/staging/sendgrid-contacts-lambda" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_sendgrid-contacts-lambda
        rules: path "secret/data/production/sendgrid-contacts-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_property-etl-lambda
        rules: path "secret/data/staging/property-etl-lambda" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_property-etl-lambda
        rules: path "secret/data/production/property-etl-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_compass-lead-lambda
        rules: path "secret/data/staging/compass-lead-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_production_compass-lead-lambda
        rules: path "secret/data/production/compass-lead-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_data-sync-service
        rules: path "secret/data/staging/data-sync-service" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_data-sync-service
        rules: path "secret/data/production/data-sync-service" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_ai-marketing-scheduler
        rules: path "secret/data/staging/ai-marketing-scheduler" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_ai-marketing-scheduler
        rules: path "secret/data/production/ai-marketing-scheduler" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_datakraken
        rules: path "secret/data/staging/datakraken" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_read_secrets_development_datakraken
        rules: path "secret/data/development/datakraken" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_read_secrets_production_datakraken
        rules: path "secret/data/production/datakraken" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_read_secrets_staging_geocode-api
        rules: path "secret/data/staging/geocode-api" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_geocode-api
        rules: path "secret/data/production/geocode-api" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_christies-lambda
        rules: path "secret/data/staging/christies-lambda" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_christies-lambda
        rules: path "secret/data/production/christies-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_serhant-lambda
        rules: path "secret/data/staging/serhant-lambda" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_serhant-lambda
        rules: path "secret/data/production/serhant-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_snapshot-lambda
        rules: path "secret/data/staging/snapshot-lambda" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_snapshot-lambda
        rules: path "secret/data/production/snapshot-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_rss-feed-lambda
        rules: path "secret/data/staging/rss-feed-lambda" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_rss-feed-lambda
        rules: path "secret/data/production/rss-feed-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_bloom-scheduler-lambda
        rules: path "secret/data/staging/bloom-scheduler-lambda" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_bloom-scheduler-lambda
        rules: path "secret/data/production/bloom-scheduler-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_seo-automation-api-gateway
        rules: path "secret/data/staging/seo-automation" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_seo-automation-api-gateway
        rules: path "secret/data/production/seo-automation" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_client-marketing-service
        rules: path "secret/data/staging/client-marketing-service" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_client-marketing-service
        rules: path "secret/data/production/client-marketing-service" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_lofty-lambda
        rules: path "secret/data/staging/lofty-lambda" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_lofty-lambda
        rules: path "secret/data/production/lofty-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_post-publishing-lambda
        rules: path "secret/data/staging/post-publishing-lambda" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_post-publishing-lambda
        rules: path "secret/data/production/post-publishing-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_cloze-lambda
        rules: path "secret/data/staging/cloze-lambda" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_cloze-lambda
        rules: path "secret/data/production/cloze-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_email-scheduler-lambda
        rules: path "secret/data/staging/email-scheduler-lambda" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_email-scheduler-lambda
        rules: path "secret/data/production/email-scheduler-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_c21-redwood-lambda
        rules: path "secret/data/staging/c21-redwood-lambda" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_c21-redwood-lambda
        rules: path "secret/data/production/c21-redwood-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_followupboss-import-leads-lambda
        rules: path "secret/data/staging/followupboss-import-leads-lambda" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_followupboss-import-leads-lambda
        rules: path "secret/data/production/followupboss-import-leads-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_anywhere-lambda
        rules: path "secret/data/staging/anywhere-lambda" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_anywhere-lambda
        rules: path "secret/data/production/anywhere-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_moxi-lambda
        rules: path "secret/data/staging/moxi-lambda" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_moxi-lambda
        rules: path "secret/data/production/moxi-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_rechat-lambda
        rules: path "secret/data/staging/rechat-lambda" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_rechat-lambda
        rules: path "secret/data/production/rechat-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_property-etl
        rules: path "secret/data/staging/property-extractor" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_property-etl
        rules: path "secret/data/production/property-extractor" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_staging_website-provider
        rules: path "secret/data/staging/website-provider" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_website-provider
        rules: path "secret/data/production/website-provider" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_staging_website-service
        rules: path "secret/data/staging/website-service" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_staging_builder-cms-lambda
        rules: path "secret/data/staging/builder-cms-lambda" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_website-service
        rules: path "secret/data/production/website-service" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_builder-cms-lambda
        rules: path "secret/data/production/builder-cms-lambda" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_staging_broken-link-checker
        rules: path "secret/data/staging/broken-link-checker" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_broken-link-checker
        rules: path "secret/data/production/broken-link-checker" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_staging_website-sns-producer
        rules: path "secret/data/staging/website-sns-producer" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_website-sns-producer
        rules: path "secret/data/production/website-sns-producer" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_staging_mls-search
        rules: path "secret/data/staging/mls-search" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_mls-search
        rules: path "secret/data/production/mls-search" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_staging_network-scene-gateway
        rules: path "secret/data/staging/network-scene-gateway" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_network-scene-gateway
        rules: path "secret/data/production/network-scene-gateway" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_staging_network-kafka-consumer
        rules: path "secret/data/staging/networks-kafka-consumer" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_network-kafka-consumer
        rules: path "secret/data/production/networks-kafka-consumer" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_staging_import-map-deployer
        rules: path "secret/data/staging/import-map-deployer" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_import-map-deployer
        rules: path "secret/data/production/import-map-deployer" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_databada-root-credentials
        rules: path "secret/data/production/databada-root-credentials*" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_staging_databada-root-credentials
        rules: path "secret/data/staging/databada-root-credentials*" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_standard
        rules: path "secret/data/production/standard" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_staging_standard
        rules: path "secret/data/staging/standard" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_staging_audit-log-service
        rules: path "secret/data/staging/audit-log-service" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_staging_slide
        rules: path "secret/data/staging/slide*" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_slide
        rules: path "secret/data/production/slide*" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_ops_database-info
        rules: path "secret/data/operations/database-info/*" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_ops_all
        rules: path "secret/data/operations/*" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_staging_database-info
        rules: path "secret/data/staging/database-info/*" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_database-info
        rules: path "secret/data/production/database-info/*" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_operations_database-info
        rules: path "secret/data/operations/database-info/*" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_operations_sonar-db-user
        rules: path "secret/data/operations/sonar-db-user/*" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_operations_global
        rules: path "secret/data/operations/global/*" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_secrets_production_metabase-db-user
        rules: path "secret/data/production/metabase-db-user*" {
          capabilities = ["read", "list"]
          }

      - name: allow_list_secrets_all
        rules: path "secret/*" {
          capabilities = ["list"]
          }

      - name: allow_list_db_all
        rules: path "database/*" {
          capabilities = ["list"]
          }

      - name: allow_manage_secrets_all
        rules: path "secret/*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_read_db_staging_all
        rules: path "database/creds/postgres-staging*" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_db_production_all
        rules: path "database/creds/postgres-production*" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_db_production_mls-data-related
        rules: path "database/creds/postgres-production-property-long-life*" {
            capabilities = ["read", "list"]
          }

      - name: full_access
        rules: path "*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

          path "secret/*" {
            capabilities = ["create", "read", "update", "list", "delete"]
          }

          path "database/*" {
            capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_db_production_slide
        rules: path "database/creds/postgres-production-slide-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_db_production_buyerseller
        rules: path "database/creds/postgres-production-buyerseller-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_db_staging_buyerseller
        rules: path "database/creds/postgres-staging-buyerseller-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_db_production_notification
        rules: path "database/creds/postgres-production-notification-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_db_staging_notification
        rules: path "database/creds/postgres-staging-notification-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_db_staging_mls
        rules: path "database/creds/postgres-staging-mls-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_db_production_identity
        rules: path "database/creds/postgres-production-identity-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_db_staging_identity
        rules: path "database/creds/postgres-staging-identity-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_db_production_ai
        rules: path "database/creds/postgres-production-ai-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_db_production_crew_ai_seo_automation
        rules: path "database/creds/postgres-production-crew-ai-seo-automation-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_db_staging_ai
        rules: path "database/creds/postgres-staging-ai-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_db_production_pln_team
        rules: path "database/creds/postgres-production-pln-team-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_read_secrets_staging_tenant-service
        rules: path "secret/data/staging/tenant-service" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_production_tenant-service
        rules: path "secret/data/production/tenant-service" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_cms-service
        rules: path "secret/data/staging/cms-service" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_production_cms-service
        rules: path "secret/data/production/cms-service" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_crm-service
        rules: path "secret/data/staging/crm-service" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_production_crm-service
        rules: path "secret/data/production/crm-service" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_ops-service
        rules: path "secret/data/staging/ops-service" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_production_ops-service
        rules: path "secret/data/production/ops-service" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_crm-consumer
        rules: path "secret/data/staging/crm-consumer" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_production_crm-consumer
        rules: path "secret/data/production/crm-consumer" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_property-linking-lambda
        rules: path "secret/data/staging/property-linking-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_production_property-linking-lambda
        rules: path "secret/data/production/property-linking-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_db_staging_all_main_dev
        rules: path "database/creds/postgres-staging-all_main-dev" {
          capabilities = ["create", "read", "list"]
          }

      - name: allow_db_production_core
        rules: path "database/creds/postgres-production-core-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

          path "database/creds/postgres-production-core-short-for-test*" {
            capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_db_production_search
        rules: path "database/creds/postgres-production-search-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_db_staging_search
        rules: path "database/creds/postgres-staging-search-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_db_staging_testvaultlambda
        rules: path "database/creds/postgres-staging-testvaultlambda-long-life*" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_read_secrets_staging_search-service
        rules: path "secret/data/staging/search-service" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_production_search-service
        rules: path "secret/data/production/search-service" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_search-kafka-consumer
        rules: path "secret/data/staging/search-kafka-consumer" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_production_search-kafka-consumer
        rules: path "secret/data/production/search-kafka-consumer" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_lead_ingester
        rules: path "secret/data/staging/lead-ingester" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_read_secrets_production_lead_ingester
        rules: path "secret/data/production/lead-ingester" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_read_secrets_staging_notification_service
        rules: path "secret/data/staging/notification-service" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_read_secrets_production_notification_service
        rules: path "secret/data/production/notification-service" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_circle_ci_config
        rules: path "secret/data/circleci/config" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_read_secrets_staging_website-api-gateway
        rules: path "secret/data/staging/website-api-gateway" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_home-search-tests-ete
        rules: path "secret/data/staging/home-search-tests-ete" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_production_website-api-gateway
        rules: path "secret/data/production/website-api-gateway" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_divolte-service
        rules: path "secret/data/staging/divolte-service" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_production_divolte-service
        rules: path "secret/data/production/divolte-service" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_website-tests-e2e
        rules: path "secret/data/staging/website-tests-e2e" {
          capabilities = ["read", "list"]
          }

      - name: allow_write_secrets_staging_website-tests-e2e
        rules: path "secret/data/staging/website-tests-e2e" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_read_secrets_staging_web_platform_tests_e2e
        rules: path "secret/data/staging/web-platform-tests-e2e" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_clickstream-s3-es
        rules: path "secret/data/staging/clickstream-s3-es" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_data_syndication_lambda
        rules: path "secret/data/staging/data-syndication-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_production_clickstream-s3-es
        rules: path "secret/data/production/clickstream-s3-es" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_email-feedback-lambda
        rules: path "secret/data/staging/email-feedback-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_production_email-feedback-lambda
        rules: path "secret/data/production/email-feedback-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_production_data_syndication_lambda
        rules: path "secret/data/production/data-syndication-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_production_auth0_configuration
        rules: path "secret/data/production/auth0-configuration" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_auth0_configuration
        rules: path "secret/data/staging/auth0-configuration" {
          capabilities = ["read", "list"]
          }

      - name: allow_raft_snapshot
        rules: path "sys/storage/raft/snapshot" {
          capabilities = ["create", "read", "update", "delete", "list", "sudo"]
          }

          path "sys/storage/raft/snapshot-force" {
          capabilities = ["create", "read", "update", "delete", "list", "sudo"]
          }

      - name: allow_atlantis
        rules: path "secret/data/infrastructure/atlantis" {
          capabilities = ["read", "list"]
          }
      - name: allow_release_notifications
        rules: path "secret/data/infrastructure/release-notifications" {
          capabilities = ["read", "list"]
          }

      - name: allow_image_pull_secret
        rules: path "secret/data/infrastructure/image-pull-secret" {
          capabilities = ["read", "list"]
          }

      - name: allow_datadog
        rules: path "secret/data/infrastructure/datadog" {
          capabilities = ["read", "list"]
          }
      - name: allow_confluent_cloud
        rules: path "secret/data/infrastructure/confluent-cloud" {
          capabilities = ["read", "list"]
          }
      - name: allow_read_infrastructure_all
        rules: path "secret/data/infrastructure/*" {
          capabilities = ["read", "list"]
          }
      - name: allow_corridor
        rules: path "secret/data/infrastructure/corridor" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_cms_service
        rules: path "secret/data/staging/cms-service" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_read_secrets_production_cms_service
        rules: path "secret/data/production/cms-service" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_read_secrets_staging_crm_service
        rules: path "secret/data/staging/crm-service" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_read_secrets_production_crm_service
        rules: path "secret/data/production/crm-service" {
          capabilities = ["create", "read", "update", "list", "delete"]
          }

      - name: allow_read_secrets_staging_ai_agents
        rules: path "secret/data/staging/ai-agents" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_crew_ai_seo_automation
        rules: path "secret/data/staging/crew-ai-seo-automation" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_ailn-v2-followup-lambda
        rules: path "secret/data/staging/ailn-v2-followup-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_production_ailn-v2-followup-lambda
        rules: path "secret/data/production/ailn-v2-followup-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_ailn-lambda
        rules: path "secret/data/staging/ailn-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_production_ailn-lambda
        rules: path "secret/data/production/ailn-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_staging_workflow_automation_lambda
        rules: path "secret/data/staging/workflow-automation-lambda" {
          capabilities = ["read", "list"]
          }

      - name: allow_read_secrets_production_workflow_automation_lambda
        rules: path "secret/data/production/workflow-automation-lambda" {
          capabilities = ["read", "list"]
          }

    auth:
      - type: oidc
        path: oidc
        config:
          oidc_discovery_url: "https://${env `AUTH0_DOMAIN`}/"
          oidc_client_id: "${env `AUTH0_CLIENT_ID`}"
          oidc_client_secret: "${env `AUTH0_CLIENT_SECRET`}"
          default_role: "default"
        roles:
          - name: default
            bound_audiences: "${env `AUTH0_CLIENT_ID`}"
            allowed_redirect_uris: "https://vault.ops.luxurycoders.com/ui/vault/auth/oidc/oidc/callback"
            user_claim: "sub"
            role_type: "oidc"
            groups_claim: "https://luxurycoders.com/roles"
            policies:
              - allow_list_secrets_all
              - allow_list_db_all
      - type: kubernetes
        path: kubernetes-ops
        roles:
          - name: default
            bound_service_account_names: ["*"]
            bound_service_account_namespaces: ["default", "qa"]
            ttl: "168h"
          - name: prometheus-role
            bound_service_account_names: ["*"]
            bound_service_account_namespaces: ["*"]
            policies:
              - allow_read_secrets_operations_prometheus
            ttl: "168h"
          - name: global-role
            bound_service_account_names: ["*"]
            bound_service_account_namespaces: ["grafana"]
            policies:
              - allow_read_secrets_operations_global
            ttl: "168h"
          - name: buyer-seller-service
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "qa" ]
            policies:
              - allow_read_secrets_qa_buyer-seller
              - allow_read_secrets_qa_all

          - name: generic # used to simplify access to secrets for QA envs
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "*" ]
            policies:
              - allow_read_secrets_qa_all
              - allow_read_secrets_ops_all
            ttl: "168h"

          - name: circleci-runner # used to simplify access to secrets for QA envs
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "circleci" ]
            policies:
              - allow_circle_ci_config

          - name: vault-backup
            bound_service_account_names: [ "vault-backup" ]
            bound_service_account_namespaces: [ "vault-backup" ]
            policies:
              - allow_raft_snapshot

          - name: atlantis
            bound_service_account_names: [ "atlantis" ]
            bound_service_account_namespaces: [ "atlantis" ]
            policies:
              - allow_atlantis
              - allow_confluent_cloud

          - name: release-notifications
            bound_service_account_names: [ "release-notifications" ]
            bound_service_account_namespaces: [ "release-notifications" ]
            policies:
              - allow_release_notifications
              - allow_image_pull_secret

          - name: infrastructure
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "*" ]
            policies:
              - allow_read_infrastructure_all

          - name: n8n
            bound_service_account_names: [ "n8n" ]
            bound_service_account_namespaces: [ "n8n" ]
            policies:
              - allow_read_secrets_operations_n8n
            ttl: "168h"

      - type: kubernetes
        path: kubernetes-production
        config:
          disable_local_ca_jwt: true
          kubernetes_ca_cert: |
            -----BEGIN CERTIFICATE-----
            MIIDBTCCAe2gAwIBAgIIO2PAqzg9G/owDQYJKoZIhvcNAQELBQAwFTETMBEGA1UE
            AxMKa3ViZXJuZXRlczAeFw0yNDA3MDkyMjIyMzRaFw0zNDA3MDcyMjI3MzRaMBUx
            EzARBgNVBAMTCmt1YmVybmV0ZXMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
            AoIBAQDHTdxl1FzARLFYOgHLb/ysETHQprE9EgmXjqKmHMv56ryj1BuYJ8sJ/EUh
            e/hVDOLISu2r1xcVyHLzHWMQGO4uR/Fs4KBwm+lf7rjdjPCFzODKRUZh5ql4IbvN
            l64/KiOTJPS68dJRUS3WrCu05Pr2NeTe/3oLm68Aae64iBojLGBMtFl29uZ5LB6H
            ZygwzEE5/f3hD9/ziy8xk5D0n3WP/WSHvX6CGjvVgCweVOjIQvlNyiLBuG+vOVpp
            Pgx9RCDig/e9Bjw6ESjhkKV7+6XfmUmWLJrsRyE5PeWROkEHHj8THf2g/PnR4S8R
            gws/AMwhgXf8tbCJwgwXL02TepVFAgMBAAGjWTBXMA4GA1UdDwEB/wQEAwICpDAP
            BgNVHRMBAf8EBTADAQH/MB0GA1UdDgQWBBQpVrDEt5gnEn+r3exd9dymvFzjMzAV
            BgNVHREEDjAMggprdWJlcm5ldGVzMA0GCSqGSIb3DQEBCwUAA4IBAQBZvvUV4Hyv
            Vm6nmq8VULXZKowagZCOYgHqp/ExYAZNQ9cFbrCi8aNmlKde6OBltMxLK7qt9K2+
            YzFL6aECuC3T+GM45IYwcubQOFtMWaxzzFkxa7yRRhHWfJEkr7yuAQRulop62pe/
            jS3ZxjI6kasIXfeCayJsZavqEdFJfMwuW3ToLoUUGWT/FxSXpVS7VDhoeNqG7xzE
            OaiPtA6XfVSMthHanawTBEFySPae6MLra4prZ5+3NPmIn04ZJ0BXS6td3j1SCqWW
            A2+ScvuzOrK8++9OcdI9IOkkpa41bcLkNRY4nOa6PYKn6qbiWAJhL/LPRv5+w7+3
            BiDHru5rULVX
            -----END CERTIFICATE-----
          kubernetes_host: https://2C8B96F9570D9DE958C717D6477E797A.gr7.us-east-1.eks.amazonaws.com
        roles:
          - name: corridor
            bound_service_account_names: [ "corridor" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_corridor
          - name: datadog
            bound_service_account_names: [ "datadog", "external-secrets" ]
            bound_service_account_namespaces: [ "datadog", "external-secrets" ]
            policies:
              - allow_datadog
          - name: api-gateway-sa-prod
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_production_core
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_database-info
              - allow_read_secrets_production_api-gateway
            ttl: "168h"
          - name: public-api-gateway-sa-prod
            bound_service_account_names: [ "public-api-gateway-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_production_core
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_public-api-gateway
            ttl: "168h"
          - name: property-etl-prod
            bound_service_account_names: [ "property-etl" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_production_core
              - allow_db_production_property
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_database-info
              - allow_read_secrets_production_property-etl
            ttl: "168h"
          - name: network-service-sa-prod
            bound_service_account_names: [ "network-service-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_production_network
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_network-service
              - allow_read_secrets_production_database-info
            ttl: "168h"

          - name: mls-search-sa-prod
            bound_service_account_names: [ "mls-search-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_mls-search
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_database-info
            ttl: "168h"

          - name: network-scene-gateway-prod
            bound_service_account_names: [ "network-scene-gateway" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_network-scene-gateway
            ttl: "168h"

          - name: network-kafka-consumer-prod
            bound_service_account_names: [ "network-kafka-consumer" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_network-kafka-consumer
            ttl: "168h"

          - name: notification-service-sa-prod
            bound_service_account_names: [ "notification-service-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              # - allow_read_secrets_production_standard
              - allow_read_secrets_production_database-info
              - allow_db_production_notification
              - allow_read_secrets_production_notification_service
            ttl: "168h"

          - name: property-service-prod
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_production_property
              - allow_read_secrets_production_databada-root-credentials # Temporary, should be changed once we decide to use rotated user
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_database-info
            ttl: "168h"

          - name: cms-service-prod
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_production_property
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_cms_service
              - allow_read_secrets_production_database-info
            ttl: "168h"

          - name: crm-consumer-prod
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_crm-consumer
              - allow_read_secrets_production_crm-service
              - allow_db_production_crm
              - allow_read_secrets_production_database-info
            ttl: "168h"

          - name: crm-service-prod
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_crm_service
              - allow_db_production_crm
              - allow_read_secrets_production_database-info
            ttl: "168h"

          - name: ops-service-sa-prod
            bound_service_account_names: [ "ops-service-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_ops-service
              - allow_read_secrets_production_database-info
              - allow_db_production_ops_service
            ttl: "168h"

          - name: website-service-prod
            bound_service_account_names: [ "website-service" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_production_website
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_database-info
              - allow_read_secrets_production_website-service
            ttl: "168h"

          - name: broken-link-checker-production
            bound_service_account_names: [ "broken-link-checker-production" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_broken-link-checker
            ttl: "168h"

          - name: website-sns-producer-production
            bound_service_account_names: [ "website-sns-producer-production" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_website-sns-producer
            ttl: "168h"

          - name: website-provider-sa-prod
            bound_service_account_names: [ "website-provider-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_website-provider
            ttl: "168h"

          - name: render-service-prod
            bound_service_account_names: [ "render-service" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_production_website
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_database-info
              - allow_read_secrets_production_website-service
            ttl: "168h"

          - name: purge-service-sa-prod
            bound_service_account_names: [ "purge-service-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_production_website
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_database-info
              - allow_read_secrets_production_website-service
            ttl: "168h"

          - name: vault-liveness-tester-sa
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_staging_core # remove later
              - allow_read_secrets_staging_database-info # remove later
              - allow_db_production_core
              - allow_read_secrets_production_database-info
            ttl: "10m"

          - name: metabase-sa
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps"]
            policies:
              - allow_read_secrets_production_metabase-db-user
            ttl: "168h"

          - name: slide-provider-prod
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard

          - name: slide-scene-gateway-sa-prod
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_slide

          - name: slide-service-prod
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_db_production_slide
              - allow_read_secrets_production_database-info

          - name: presence-import-map-deployer-sa-prod
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_import-map-deployer

          - name: slide-scene-micro-installer-sa
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_import-map-deployer

          - name: microfrontend-installer
            bound_service_account_names:
              - slide-scene-micro-installer-sa
              - network-scene-micro-installer-sa
              - reporting-scene-micro-installer-sa
              - website-builder-scene-micro-installer-sa
              - boards-scene-micro-installer-sa
              - onboarding-hub-micro-installer-sa
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_import-map-deployer
              - allow_read_secrets_production_standard

          - name: load-slides-installer-prod
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_database-info
              - allow_db_production_slide

          - name: load-migrations-production
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_databada-root-credentials
              - allow_read_secrets_production_database-info
              - allow_read_secrets_production_notification_service
              - allow_db_production_search
              - allow_db_production_notification

          - name: buyer-seller-service-prod
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_buyer-seller
              - allow_db_production_buyerseller
              - allow_read_secrets_production_database-info
              - allow_read_secrets_production_api-gateway

          - name: tenant-service-prod
            bound_service_account_names: [ "tenant-service", "external-secrets" ]
            bound_service_account_namespaces: [ "apps", "external-secrets" ]
            policies:
              - allow_db_production_identity
              - allow_read_secrets_production_database-info
              - allow_read_secrets_production_tenant-service
              - allow_read_secrets_production_standard

          - name: website-api-gateway-prod
            bound_service_account_names: [ "website-api-gateway-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_website-api-gateway

          - name: divolte-service-prod
            bound_service_account_names: [ "divolte-service" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_divolte-service

          - name: clickstream-api-prod
            bound_service_account_names: [ "clickstream-api-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard

          - name: lead-ingester-prod
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_lead_ingester

          - name: lead-preparer-installer-sa-prod
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard

          - name: liveness-checker-lambda-installer-sa
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_liveness_checker
              - allow_read_secrets_production_liveness_checker

          - name: compass-lead-lambda-installer-sa
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_compass-lead-lambda
              - allow_read_secrets_staging_standard
              - allow_read_secrets_production_compass-lead-lambda
              - allow_read_secrets_production_standard

          - name: search-service-prod
            bound_service_account_names: [ "search-service" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_database-info
              - allow_db_production_search
              - allow_read_secrets_production_search-service
              - allow_read_secrets_production_standard

          - name: search-service-file-processor
            bound_service_account_names: [ "search-service-file-processor" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_database-info
              - allow_db_production_search
              - allow_read_secrets_production_search-service
              - allow_read_secrets_production_standard

          - name: search-kafka-consumer-prod
            bound_service_account_names: [ "search-kafka-consumer" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_database-info
              - allow_db_production_search
              - allow_read_secrets_production_search-kafka-consumer

          - name: website-status-service-installer-sa-prod
            bound_service_account_names: [ "website-status-service-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_website-status-service

          - name: mls-data-etl-lookupapi-sa-prod
            bound_service_account_names: [ "mls-data-etl-lookupapi-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_mls-data-etl-lookupapi
              - allow_read_secrets_production_datakraken

          - name: mls-data-ai-agent-prod
            bound_service_account_names: [ "mls-data-ai-agent" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_datakraken

          - name: mls-data-etl-aitriage-sa-prod
            bound_service_account_names: [ "mls-data-etl-aitriage-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_mls-data-etl-aitriage
              - allow_read_secrets_production_datakraken

          - name: realogy-lead-router-lambda-installer-sa-prod
            bound_service_account_names: [ "realogy-lead-router-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_realogy-lead-router-lambda

          - name: webhook-lambda-installer-sa-prod
            bound_service_account_names: [ "webhook-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_webhook-lambda-lambda

          - name: followupboss-lambda-installer-sa-prod
            bound_service_account_names: [ "followupboss-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_followupboss-lambda

          - name: sendgrid-contacts-lambda-installer-sa-prod
            bound_service_account_names: [ "sendgrid-contacts-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_sendgrid-contacts-lambda

          - name: property-etl-lambda-installer-sa-prod
            bound_service_account_names: [ "property-etl-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_property-etl-lambda
              - allow_read_secrets_production_database-info
              - allow_read_secrets_production_databada-root-credentials

          - name: data-sync-service-installer-sa-prod
            bound_service_account_names: [ "data-sync-service-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_data-sync-service

          - name: datakraken-installer-sa-prod
            bound_service_account_names: [ "datakraken-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_datakraken

          - name: website-templates-prod
            bound_service_account_names: [ "website-templates" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard

          - name: geocode-api-installer-sa-prod
            bound_service_account_names: [ "geocode-api-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_geocode-api
              - allow_read_secrets_production_datakraken

          - name: christies-lambda-installer-sa-prod
            bound_service_account_names: [ "christies-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_christies-lambda

          - name: serhant-lambda-installer-sa-prod
            bound_service_account_names: [ "serhant-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_serhant-lambda

          - name: snapshot-lambda-installer-sa-prod
            bound_service_account_names: [ "snapshot-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_snapshot-lambda

          - name: bloom-scheduler-lambda-installer-sa-prod
            bound_service_account_names: [ "bloom-scheduler-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_bloom-scheduler-lambda
              - allow_read_secrets_production_standard

          - name: seo-automation-api-gateway-sa-prod
            bound_service_account_names: [ "seo-automation-api-gateway-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_seo-automation-api-gateway
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_database-info
              - allow_db_production_crew_ai_seo_automation

          - name: client-marketing-service-sa-prod
            bound_service_account_names: [ "client-marketing-service-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_client-marketing-service
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_database-info
              - allow_db_production_crew_ai_seo_automation

          - name: lofty-lambda-installer-sa-prod
            bound_service_account_names: [ "lofty-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_lofty-lambda
              - allow_read_secrets_production_standard

          - name: cmt-web-scraper-lambda-installer-sa-prod
            bound_service_account_names: [ "cmt-web-scraper-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_cmt-web-scraper-lambda

          - name: media-lambda-installer-sa-prod
            bound_service_account_names: [ "media-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_api-gateway
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_media-lambda

          - name: post-publishing-lambda-installer-sa-prod
            bound_service_account_names: [ "post-publishing-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_post-publishing-lambda
              - allow_read_secrets_production_standard

          - name: email-feedback-lambda-installer-sa-prod
            bound_service_account_names: [ "email-feedback-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_email-feedback-lambda

          - name: email-scheduler-lambda-installer-sa-prod
            bound_service_account_names: [ "email-scheduler-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_post-publishing-lambda
              - allow_read_secrets_production_email-scheduler-lambda
              - allow_read_secrets_production_standard

          - name: cloze-lambda-installer-sa-prod
            bound_service_account_names: [ "cloze-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_cloze-lambda
              - allow_read_secrets_production_standard

          - name: rss-feed-lambda-installer-sa-prod
            bound_service_account_names: [ "rss-feed-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_rss-feed-lambda
              - allow_read_secrets_production_api-gateway

          - name: c21-redwood-lambda-installer-sa-prod
            bound_service_account_names: [ "c21-redwood-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_c21-redwood-lambda

          - name: followupboss-import-leads-lambda-installer-sa-prod
            bound_service_account_names: [ "followupboss-import-leads-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_production_core
              - allow_read_secrets_production_database-info
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_followupboss-import-leads-lambda

          - name: moxi-lambda-installer-sa-prod
            bound_service_account_names: [ "moxi-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_moxi-lambda

          - name: anywhere-lambda-installer-sa-prod
            bound_service_account_names: [ "anywhere-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_anywhere-lambda
              - allow_read_secrets_production_database-info

          - name: rechat-lambda-installer-sa-prod
            bound_service_account_names: [ "rechat-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_rechat-lambda

          - name: mls-data-etl-sa-prod
            bound_service_account_names: [ "mls-data-etl-sa", "external-secrets"]
            bound_service_account_namespaces: [ "mwaa-production", "external-secrets"]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_datakraken
            ttl: "12h"

          - name: mls-data-etl-media-streaming-sa-prod
            bound_service_account_names: [ "mls-data-etl-media-streaming", "external-secrets"]
            bound_service_account_namespaces: [ "apps", "external-secrets"]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_datakraken

          - name: mls-data-etl-geocoding-streaming-sa-prod
            bound_service_account_names: [ "mls-data-etl-geocoding-streaming", "external-secrets"]
            bound_service_account_namespaces: [ "apps", "external-secrets"]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_datakraken

          - name: mls-data-etl-streaming-orchestrator-sa-prod
            bound_service_account_names: [ "mls-data-etl-streaming-orchestrator", "external-secrets"]
            bound_service_account_namespaces: [ "apps", "external-secrets"]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_datakraken

          - name: mls-data-etl-clickstream-sa-prod
            bound_service_account_names: [ "mls-data-etl-clickstream-sa", "external-secrets"]
            bound_service_account_namespaces: [ "apps", "external-secrets"]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_datakraken

          - name: property-linking-lambda-installer-sa-prod
            bound_service_account_names: [ "property-linking-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_production_property
              - allow_read_secrets_production_database-info
              - allow_read_secrets_production_property-linking-lambda
            ttl: "168h"

          - name: ai-lead-nurturing-lambda-installer-sa-prod
            bound_service_account_names: [ "ai-lead-nurturing-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_production_core
              - allow_read_secrets_production_database-info
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_ailn-lambda

          - name: structurely-create-conv-lambda-installer-sa-prod
            bound_service_account_names: [ "structurely-create-conv-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_production_core
              - allow_read_secrets_production_database-info
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_ailn-lambda

          - name: ailn-v2-inbound-lambda-installer-sa-prod
            bound_service_account_names: [ "ailn-v2-inbound-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_production_core
              - allow_read_secrets_production_database-info
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_crm_service
              - allow_read_secrets_production_api-gateway
              - allow_read_secrets_production_ailn-lambda

          - name: website-ssr-service-sa-prod
            bound_service_account_names: [ "website-ssr-service-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_website-service

          - name: builder-cms-lambda-installer-sa-prod
            bound_service_account_names: [ "builder-cms-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_builder-cms-lambda

          - name: data-syndication-lambda-installer-sa-prod
            bound_service_account_names: [ "data-syndication-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_data_syndication_lambda

          - name: clickstream-s3-es-installer-sa-prod
            bound_service_account_names: [ "clickstream-s3-es-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_clickstream-s3-es

          - name: fluentbit-sa
            bound_service_account_names: [ "fluentbit-sa", "external-secrets" ]
            bound_service_account_namespaces: [ "logging", "logging-mezmo", "external-secrets" ]
            policies:
              - allow_read_secrets_production_standard

          - name: auth0-configuration
            bound_service_account_names: [ "external-secrets" ]
            bound_service_account_namespaces: [ "apps", "external-secrets" ]
            policies:
              - allow_read_secrets_production_auth0_configuration

          - name: infrastructure
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "*" ]
            policies:
              - allow_read_infrastructure_all

          - name: network-company-lambda-installer-sa
            bound_service_account_names: [ "network-company-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard

          - name: ingress-nginx
            bound_service_account_names: [ "external-secrets" ]
            bound_service_account_namespaces: [ "external-secrets" ]
            policies:
              - allow_read_secrets_production_cms-service
              - allow_read_secrets_production_tenant-service
              - allow_read_secrets_production_crm-service

          - name: ailn-v2-followup-lambda-installer-sa-prod
            bound_service_account_names: [ "ailn-v2-followup-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_production_core
              - allow_read_secrets_production_database-info
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_ailn-v2-followup-lambda
              - allow_read_secrets_production_ailn-lambda

          - name: ai-marketing-scheduler-lambda-installer-sa-prod
            bound_service_account_names: [ "ai-marketing-scheduler-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_seo-automation-api-gateway
              - allow_read_secrets_production_client-marketing-service
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_ai-marketing-scheduler

          - name: workflow-automation-lambda-installer-sa-prod
            bound_service_account_names: [ "workflow-automation-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_production_standard
              - allow_read_secrets_production_workflow_automation_lambda

      - type: kubernetes
        path: kubernetes-staging
        config:
          disable_local_ca_jwt: true
          kubernetes_ca_cert: |
            -----BEGIN CERTIFICATE-----
            MIIDBTCCAe2gAwIBAgIIAcpxreeOq8cwDQYJKoZIhvcNAQELBQAwFTETMBEGA1UE
            AxMKa3ViZXJuZXRlczAeFw0yMzEyMDUyMTI0MDVaFw0zMzEyMDIyMTI5MDVaMBUx
            EzARBgNVBAMTCmt1YmVybmV0ZXMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
            AoIBAQDQWIVBOfgwVfEbGMUtBdt/XSXOuoKscxkdMhTDf0Zm5psalJ3N6cMqgu2K
            xgE4c82H5xBjsPLlEHe06CCzfADZ9um8K6becF41Q1VM+bTxXM3BTHcKPjdDY333
            pp3OmjaQKK1n0xq/wN6kHgdgPOnByzDjzBJXjUUSM5Rmq9+GarxdTLjMWiEJmW+F
            ZnpU2JaksfyfFAWU3TgfD7u1xZEiVQD5iEoEUmD+zacYzM8ZIPfd1AlgrzYhXRhH
            F9x+l3B0qr92vGAfFLaIAFVktix5zCsnHhovl38ZFcDbZOocH16uiSEMwvIDQ3u6
            xJHGw1XM5qMQkFQXJJbzmDjuAc0DAgMBAAGjWTBXMA4GA1UdDwEB/wQEAwICpDAP
            BgNVHRMBAf8EBTADAQH/MB0GA1UdDgQWBBQEDWLOxK7IvT1IUOY28pRbnRx97zAV
            BgNVHREEDjAMggprdWJlcm5ldGVzMA0GCSqGSIb3DQEBCwUAA4IBAQBeIU2gaGo/
            NquPxHHO5aTWZfw4CM0qeUCD7Zean8no2Qi0BExpalP/nr+SJgwN4c4qw2Y2ZEz9
            K/xT6qmc0t2xkiPDf4iODEcx8U+SezkChEgE4DC0OKXvR0bCjnIgYVfY2H+CzyFD
            jfjBBbMcOWj00s2xFuIgRS3Rhywqqj+L++q7ebEuiw6vvdWuWFMw8pX7gqrrDCsw
            WKdaoMwsMxtOnx0q3iBBBiz9mu6D/ne7+6WxWy7fY8ZPpAPnEVs3KJEZZhUrl+BX
            lCV4f4NsKfKeS4KKcCq8VgP/Gmt6mK8pLGcHMQp1xYMpgqygDr+Nf+rH3xdFolhV
            LLOms1AR1Q85
            -----END CERTIFICATE-----
          kubernetes_host: https://0B0E16CE9DBD3BAA2DD34C6C2CA3543E.gr7.us-east-1.eks.amazonaws.com
        roles:
          - name: datadog
            bound_service_account_names: [ "datadog", "external-secrets" ]
            bound_service_account_namespaces: [ "datadog", "external-secrets" ]
            policies:
              - allow_datadog
          - name: api-gateway-sa
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_staging_core
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_database-info
              - allow_read_secrets_staging_api-gateway
            ttl: "168h"
          - name: public-api-gateway-sa
            bound_service_account_names: [ "public-api-gateway-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_staging_core
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_public-api-gateway
            ttl: "168h"
          - name: property-etl
            bound_service_account_names: [ "property-etl" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_staging_mls
              - allow_db_staging_core
              - allow_db_staging_property
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_database-info
              - allow_read_secrets_staging_property-etl
            ttl: "168h"
          - name: network-service-sa
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_staging_network
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_network-service
              - allow_read_secrets_staging_database-info
            ttl: "168h"

          - name: mls-search-sa
            bound_service_account_names: [ "mls-search-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_staging_mls
              - allow_read_secrets_staging_mls-search
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_database-info
            ttl: "168h"

          - name: network-scene-gateway
            bound_service_account_names: [ "network-scene-gateway" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_network-scene-gateway
            ttl: "168h"

          - name: network-kafka-consumer
            bound_service_account_names: [ "network-kafka-consumer" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_network-kafka-consumer
            ttl: "168h"

          - name: notification-service-sa
            bound_service_account_names: [ "notification-service-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_database-info
              - allow_db_staging_notification
              - allow_read_secrets_staging_notification_service
            ttl: "168h"

          - name: property-service
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_staging_property
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_database-info
            ttl: "168h"

          - name: cms-service
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_staging_property
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_cms_service
              - allow_read_secrets_staging_database-info
            ttl: "168h"

          - name: crm-consumer
            bound_service_account_names: [ "crm-consumer" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_staging_crm
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_crm-consumer
              - allow_read_secrets_staging_crm-service
              - allow_read_secrets_staging_database-info
            ttl: "168h"

          - name: crm-service
            bound_service_account_names: [ "crm-service" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_staging_crm
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_crm_service
              - allow_read_secrets_staging_database-info
            ttl: "168h"

          - name: ops-service-sa
            bound_service_account_names: [ "ops-service-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_ops-service
              - allow_read_secrets_staging_database-info
              - allow_db_staging_ops_service
            ttl: "168h"

          - name: slide-service-sa
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_staging_slide
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_database-info
            ttl: "168h"
          - name: website-service
            bound_service_account_names: [ "website-service" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_staging_website
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_database-info
              - allow_read_secrets_staging_website-service
            ttl: "168h"

          - name: broken-link-checker-staging
            bound_service_account_names: [ "broken-link-checker-staging" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_broken-link-checker
            ttl: "168h"

          - name: website-sns-producer-staging
            bound_service_account_names: [ "website-sns-producer-staging" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_website-sns-producer
            ttl: "168h"

          - name: website-provider-sa
            bound_service_account_names: [ "website-provider-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_website-provider
            ttl: "168h"

          - name: render-service
            bound_service_account_names: [ "render-service" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_staging_website
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_database-info
              - allow_read_secrets_staging_website-service
            ttl: "168h"

          - name: purge-service-sa
            bound_service_account_names: [ "purge-service-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_staging_website
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_database-info
              - allow_read_secrets_staging_website-service
            ttl: "168h"

          - name: slide-provider
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard

          - name: slide-scene-gateway-sa
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_slide

          - name: slide-service
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_db_staging_slide
              - allow_read_secrets_staging_database-info

          - name: presence-import-map-deployer-sa
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_import-map-deployer

          - name: slide-scene-micro-installer-sa
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_import-map-deployer

          - name: microfrontend-installer
            bound_service_account_names:
              - slide-scene-micro-installer-sa
              - network-scene-micro-installer-sa
              - reporting-scene-micro-installer-sa
              - website-builder-scene-micro-installer-sa
              - boards-scene-micro-installer-sa
              - onboarding-hub-micro-installer-sa
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_import-map-deployer
              - allow_read_secrets_staging_standard

          - name: load-slides-installer-sa
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_staging_slide
              - allow_read_secrets_staging_database-info

          - name: load-migrations-staging
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_databada-root-credentials
              - allow_read_secrets_staging_database-info
              - allow_read_secrets_staging_notification_service
              - allow_db_staging_search
              - allow_db_staging_notification

          - name: buyer-seller-service
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_buyer-seller
              - allow_db_staging_buyerseller
              - allow_read_secrets_staging_database-info
              - allow_read_secrets_staging_api-gateway

          - name: tenant-service
            bound_service_account_names: [ "tenant-service", "external-secrets" ]
            bound_service_account_namespaces: [ "apps", "external-secrets" ]
            policies:
              - allow_db_staging_identity
              - allow_read_secrets_staging_database-info
              - allow_read_secrets_staging_tenant-service
              - allow_read_secrets_staging_standard

          - name: website-api-gateway
            bound_service_account_names: [ "website-api-gateway-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_website-api-gateway

          - name: home-search-tests-ete
            bound_service_account_names: [ "home-search-tests-ete" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_home-search-tests-ete
              - allow_write_secrets_staging_website-tests-e2e

          - name: divolte-service
            bound_service_account_names: [ "divolte-service" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_divolte-service

          - name: clickstream-api
            bound_service_account_names: [ "clickstream-api-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard

          - name: lead-ingester
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_lead_ingester

          - name: lead-preparer-installer-sa-staging
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard

          - name: liveness-checker-lambda-installer-sa
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_liveness_checker

          - name: compass-lead-lambda-installer-sa
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_compass-lead-lambda
              - allow_read_secrets_staging_standard

          - name: search-service
            bound_service_account_names: [ "search-service" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_database-info
              - allow_db_staging_search
              - allow_read_secrets_staging_search-service
              - allow_read_secrets_staging_standard

          - name: search-service-file-processor
            bound_service_account_names: [ "search-service-file-processor" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_database-info
              - allow_db_staging_search
              - allow_read_secrets_staging_search-service
              - allow_read_secrets_staging_standard

          - name: mls-data-ai-agent
            bound_service_account_names: [ "mls-data-ai-agent" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_database-info
              - allow_db_staging_search
              - allow_read_secrets_staging_search-service

          - name: search-kafka-consumer
            bound_service_account_names: [ "search-kafka-consumer" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_database-info
              - allow_db_staging_search
              - allow_read_secrets_staging_search-kafka-consumer

          - name: website-status-service-installer-sa
            bound_service_account_names: [ "website-status-service-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_website-status-service

          - name: mls-data-ai-agent-staging
            bound_service_account_names: [ "mls-data-ai-agent" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_datakraken

          - name: mls-data-etl-lookupapi-sa
            bound_service_account_names: [ "mls-data-etl-lookupapi-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_mls-data-etl-lookupapi
              - allow_read_secrets_staging_datakraken

          - name: mls-data-etl-aitriage-sa
            bound_service_account_names: [ "mls-data-etl-aitriage-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_mls-data-etl-aitriage
              - allow_read_secrets_staging_datakraken

          - name: realogy-lead-router-lambda-installer-sa
            bound_service_account_names: [ "realogy-lead-router-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_realogy-lead-router-lambda

          - name: webhook-lambda-installer-sa
            bound_service_account_names: [ "webhook-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_webhook-lambda-lambda

          - name: followupboss-lambda-installer-sa
            bound_service_account_names: [ "followupboss-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_followupboss-lambda

          - name: sendgrid-contacts-lambda-installer-sa
            bound_service_account_names: [ "sendgrid-contacts-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_sendgrid-contacts-lambda

          - name: property-etl-lambda-installer-sa
            bound_service_account_names: [ "property-etl-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_property-etl-lambda
              - allow_read_secrets_staging_database-info
              - allow_read_secrets_staging_databada-root-credentials

          - name: data-sync-service-installer-sa
            bound_service_account_names: [ "data-sync-service-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_data-sync-service

          - name: datakraken-installer-sa
            bound_service_account_names: [ "datakraken-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_datakraken

          - name: website-templates
            bound_service_account_names: [ "website-templates" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard

          - name: geocode-api-installer-sa
            bound_service_account_names: [ "geocode-api-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_geocode-api
              - allow_read_secrets_staging_datakraken

          - name: christies-lambda-installer-sa
            bound_service_account_names: [ "christies-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_christies-lambda

          - name: serhant-lambda-installer-sa
            bound_service_account_names: [ "serhant-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_serhant-lambda

          - name: snapshot-lambda-installer-sa
            bound_service_account_names: [ "snapshot-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_snapshot-lambda

          - name: c21-redwood-lambda-installer-sa
            bound_service_account_names: [ "c21-redwood-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_c21-redwood-lambda

          - name: followupboss-import-leads-lambda-installer-sa
            bound_service_account_names: [ "followupboss-import-leads-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_staging_core
              - allow_read_secrets_staging_database-info
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_followupboss-import-leads-lambda

          - name: moxi-lambda-installer-sa
            bound_service_account_names: [ "moxi-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_moxi-lambda

          - name: anywhere-lambda-installer-sa
            bound_service_account_names: [ "anywhere-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_anywhere-lambda
              - allow_read_secrets_staging_database-info

          - name: rechat-lambda-installer-sa
            bound_service_account_names: [ "rechat-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_rechat-lambda

          - name: website-tests-e2e
            bound_service_account_names: [ "website-service-e2e" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_website-tests-e2e
            ttl: "168h"

          - name: web-platform-tests-e2e
            bound_service_account_names: [ "web-platform-e2e", "external-secrets" ]
            bound_service_account_namespaces: [ "apps", "external-secrets" ]
            policies:
              - allow_read_secrets_staging_web_platform_tests_e2e
              - allow_read_secrets_staging_standard
            ttl: "168h"

          - name: mls-data-etl-sa
            bound_service_account_names: [ "mls-data-etl-sa", "external-secrets" ]
            bound_service_account_namespaces: [ "mwaa-staging", "external-secrets" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_datakraken
            ttl: "12h"

          - name: mls-data-etl-media-streaming-sa
            bound_service_account_names: [ "mls-data-etl-media-streaming", "external-secrets" ]
            bound_service_account_namespaces: [ "apps", "external-secrets" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_datakraken

          - name: mls-data-etl-geocoding-streaming-sa
            bound_service_account_names: [ "mls-data-etl-geocoding-streaming", "external-secrets" ]
            bound_service_account_namespaces: [ "apps", "external-secrets" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_datakraken

          - name: mls-data-etl-streaming-orchestrator-sa
            bound_service_account_names: [ "mls-data-etl-streaming-orchestrator", "external-secrets" ]
            bound_service_account_namespaces: [ "apps", "external-secrets" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_datakraken

          - name: mls-data-etl-clickstream-sa
            bound_service_account_names: [ "mls-data-etl-clickstream-sa", "external-secrets" ]
            bound_service_account_namespaces: [ "apps", "external-secrets" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_datakraken

          - name: property-linking-lambda-installer-sa
            bound_service_account_names: [ "property-linking-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_staging_property
              - allow_read_secrets_staging_database-info
              - allow_read_secrets_staging_property-linking-lambda
            ttl: "168h"

          - name: ai-lead-nurturing-lambda-installer-sa
            bound_service_account_names: [ "ai-lead-nurturing-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_staging_core
              - allow_read_secrets_staging_database-info
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_ailn-lambda

          - name: structurely-create-conv-lambda-installer-sa
            bound_service_account_names: [ "structurely-create-conv-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_staging_core
              - allow_read_secrets_staging_database-info
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_ailn-lambda

          - name: ailn-v2-inbound-lambda-installer-sa
            bound_service_account_names: [ "ailn-v2-inbound-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_staging_core
              - allow_read_secrets_staging_database-info
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_crm_service
              - allow_read_secrets_staging_api-gateway
              - allow_read_secrets_staging_ailn-lambda

          - name: luxp-seed-data-job-sa
            bound_service_account_names: [ "luxp-seed-data-job-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_databada-root-credentials
              - allow_read_secrets_staging_database-info

          - name: website-ssr-service-sa
            bound_service_account_names: [ "website-ssr-service-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_website-service

          - name: bloom-scheduler-lambda-installer-sa
            bound_service_account_names: [ "bloom-scheduler-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_bloom-scheduler-lambda
              - allow_read_secrets_staging_standard

          - name: seo-automation-api-gateway-sa
            bound_service_account_names: [ "seo-automation-api-gateway-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_seo-automation-api-gateway
              - allow_read_secrets_staging_standard
              - allow_db_staging_crew_ai_seo_automation
              - allow_read_secrets_staging_database-info

          - name: client-marketing-service-sa
            bound_service_account_names: [ "client-marketing-service-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_client-marketing-service
              - allow_read_secrets_staging_standard
              - allow_db_staging_crew_ai_seo_automation
              - allow_read_secrets_staging_database-info

          - name: lofty-lambda-installer-sa
            bound_service_account_names: [ "lofty-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_lofty-lambda
              - allow_read_secrets_staging_standard

          - name: cmt-web-scraper-lambda-installer-sa
            bound_service_account_names: [ "cmt-web-scraper-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_cmt-web-scraper-lambda

          - name: media-lambda-installer-sa
            bound_service_account_names: [ "media-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_api-gateway
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_media-lambda

          - name: polaris-firechief-lambda-installer-sa
            bound_service_account_names: [ "polaris-firechief-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_polaris-firechief

          - name: post-publishing-lambda-installer-sa
            bound_service_account_names: [ "post-publishing-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_post-publishing-lambda
              - allow_read_secrets_staging_standard

          - name: email-scheduler-lambda-installer-sa
            bound_service_account_names: [ "email-scheduler-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_post-publishing-lambda
              - allow_read_secrets_staging_email-scheduler-lambda
              - allow_read_secrets_staging_standard

          - name: cloze-lambda-installer-sa
            bound_service_account_names: [ "cloze-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_cloze-lambda
              - allow_read_secrets_staging_standard

          - name: builder-cms-lambda-installer-sa
            bound_service_account_names: [ "builder-cms-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_builder-cms-lambda

          - name: data-syndication-lambda-installer-sa
            bound_service_account_names: [ "data-syndication-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_data_syndication_lambda

          - name: clickstream-s3-es-installer-sa
            bound_service_account_names: [ "clickstream-s3-es-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_clickstream-s3-es

          - name: email-feedback-lambda-installer-sa
            bound_service_account_names: [ "email-feedback-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_email-feedback-lambda


          - name: fluentbit-sa
            bound_service_account_names: [ "fluentbit-sa", "external-secrets" ]
            bound_service_account_namespaces: [ "logging", "logging-mezmo", "external-secrets" ]
            policies:
              - allow_read_secrets_staging_standard

          - name: rss-feed-lambda-installer-sa
            bound_service_account_names: [ "rss-feed-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_rss-feed-lambda
              - allow_read_secrets_staging_api-gateway

          - name: network-company-lambda-installer-sa
            bound_service_account_names: [ "network-company-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard

          - name: auth0-configuration
            bound_service_account_names: [ "external-secrets" ]
            bound_service_account_namespaces: [ "apps", "external-secrets" ]
            policies:
              - allow_read_secrets_staging_auth0_configuration

          - name: infrastructure
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "*" ]
            policies:
              - allow_read_infrastructure_all

          - name: ingress-nginx
            bound_service_account_names: [ "external-secrets" ]
            bound_service_account_namespaces: [ "external-secrets" ]
            policies:
              - allow_read_secrets_staging_cms-service
              - allow_read_secrets_staging_tenant-service
              - allow_read_secrets_staging_crm-service

          - name: ai-agents-sa
            bound_service_account_names: [ "ai-agents-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_staging_ai_agents
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_database-info
              - allow_read_secrets_staging_ai_agents
            ttl: "168h"

          - name: crew-ai-seo-automation-sa
            bound_service_account_names: [ "crew-ai-seo-automation-staging" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_staging_crew_ai_seo_automation
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_database-info
              - allow_read_secrets_staging_crew_ai_seo_automation
            ttl: "168h"

          - name: ailn-v2-followup-lambda-installer-sa
            bound_service_account_names: [ "ailn-v2-followup-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_db_staging_core
              - allow_read_secrets_staging_database-info
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_ailn-v2-followup-lambda
              - allow_read_secrets_staging_ailn-lambda

          - name: ai-marketing-scheduler-lambda-installer-sa
            bound_service_account_names: [ "ai-marketing-scheduler-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_seo-automation-api-gateway
              - allow_read_secrets_staging_client-marketing-service
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_ai-marketing-scheduler

          - name: workflow-automation-lambda-installer-sa
            bound_service_account_names: [ "workflow-automation-lambda-installer-sa" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_standard
              - allow_read_secrets_staging_workflow_automation_lambda

          - name: audit-log-service
            bound_service_account_names: [ "audit-log-service" ]
            bound_service_account_namespaces: [ "apps" ]
            policies:
              - allow_read_secrets_staging_audit-log-service
            ttl: "168h"

      - type: kubernetes
        path: kubernetes-staging-data
        config:
          disable_local_ca_jwt: true
          kubernetes_ca_cert: |
            -----BEGIN CERTIFICATE-----
            MIIDBTCCAe2gAwIBAgIIGevdjT2jF/gwDQYJKoZIhvcNAQELBQAwFTETMBEGA1UE
            AxMKa3ViZXJuZXRlczAeFw0yNTA3MzAyMjQyNDZaFw0zNTA3MjgyMjQ3NDZaMBUx
            EzARBgNVBAMTCmt1YmVybmV0ZXMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
            AoIBAQC8mdypHk4Pgeq6fPocm+XiZSaIuzfPp51sTGC3kboPhjjlTaUvhqNER/bq
            k6UB4/vZxnVujoBblyLQBt+rHxrTJbkHClYLT0VE4CYOEtmWMpVbI19kqG5VIQUj
            lLbjUceRwh871Q4cHG8sjpptqdqj8EqYyavnJEBy7pWsZZb1cCgU5b5/15aDw/3G
            RqZxHc5YJHpIRll3lW0Gof24zwtLiLpJ7BEf6vAOwnlw4T2SJIl0oB5DVelrme5P
            8a8WPRp7clOBWsKsNucX5J5CQRswWKw7w7crV1D7VbIjVbVKktAxYIvrz4yArRlT
            1Hz8hWtEQdbm+YN+************************************/wQEAwICpDAP
            BgNVHRMBAf8EBTADAQH/MB0GA1UdDgQWBBRFBddl9Gfg2rhYa05HDxHx4F5G5jAV
            BgNVHREEDjAMggprdWJlcm5ldGVzMA0GCSqGSIb3DQEBCwUAA4IBAQC3ye2g2C2B
            uKxcpfBHeeXAe3FExMgxINwgMGucx7s+QW7ZI8kEcsuO7MNQ975SU5AyfzAPBN7O
            FYh7kSD39Zxlity5gKdUjxZA4/rx/sIv0kd6CrO54xU3trPrDEuL2LoYUDUpRNL9
            AYewQut72PHzzxNK4qq9zkcP2ch4XGrMSdCGG0uj5JsLtWbga7LWd/vFooEeRvOt
            3ERu1mEkpJw8wTE0iekNQ8ZcRj5g5gau0pVGDfvePsVPoBAEp1olmMeoMt538Pqe
            FJwSgFZZvKbcCNUu/8tpRwn3fmDl2GtNRq39WbyNAMlDgcWshi3ILJDs511Z8G/U
            aH4+a9YBt3zS
            -----END CERTIFICATE-----
          kubernetes_host: https://18382A10391BAA80C09455AFBE97F91D.gr7.us-east-1.eks.amazonaws.com
        roles:
          - name: datadog
            bound_service_account_names: [ "datadog", "external-secrets" ]
            bound_service_account_namespaces: [ "datadog", "external-secrets" ]
            policies:
              - allow_datadog
          - name: infrastructure
            bound_service_account_names: [ "*" ]
            bound_service_account_namespaces: [ "*" ]
            policies:
              - allow_read_infrastructure_all

      - type: aws
        path: aws-staging
        config:
          access_key: "${env `AWS_STAGING_ACCESS_KEY`}"
          secret_key: "${env `AWS_STAGING_SECRET_KEY`}"
        roles:
          - name: testvaultlambda-staging-us-east-1-lambdarole
            bound_iam_principal_arn: arn:aws:iam::************:role/TestVaultLambda-staging-us-east-1-lambdaRole
            policies:
              - allow_read_secrets_staging_database-info
              - allow_db_staging_testvaultlambda

    secrets:
      - path: secret
        type: kv
        description: General secrets.
        options:
          version: 2
      - path: database
        type: database
        description: Postgres Database secret engine.
        configuration:
          config:
            - name: postgres-staging-main-config-v4
              plugin_name: "postgresql-database-plugin"
              connection_url: "postgresql://{{username}}:{{password}}@${env `POSTGRES_MAIN_HOST_STAGING_V4`}:5432/postgres"
              allowed_roles:
                - postgres-staging-search-long-life-v4
                - postgres-staging-identity-long-life-v4
                - postgres-staging-notification-long-life-v4
                - postgres-staging-slide-long-life-v4
                - postgres-staging-network-long-life-v4
                - postgres-staging-property-long-life-v4
                - postgres-staging-buyerseller-long-life-v4
                - postgres-staging-website-long-life-v4
                - postgres-staging-core-long-life-v4
                - postgres-staging-all_main-dev-v4
                - postgres-staging-ai-agents-long-life-v4
                - postgres-staging-crew-ai-seo-automation-long-life-v4
                - postgres-staging-crm-long-life-v4
                - postgres-staging-ops-service-long-life-v4
              username: "${env `POSTGRES_MAIN_USER_STAGING_V4`}"
              password: "${env `POSTGRES_MAIN_PASSWORD_STAGING_V4`}"

            - name: postgres-production-main-config-v3
              plugin_name: "postgresql-database-plugin"
              connection_url: "postgresql://{{username}}:{{password}}@${env `POSTGRES_MAIN_HOST_PRODUCTION_V3`}:5432/postgres"
              allowed_roles:
                - postgres-production-slide-long-life-v3
                - postgres-production-identity-long-life-v3
                - postgres-production-network-long-life-v3
                - postgres-production-notification-long-life-v3
                - postgres-production-buyerseller-long-life-v3
                - postgres-production-property-long-life-v3
                - postgres-production-website-long-life-v3
                - postgres-production-core-long-life-v3
                - postgres-production-core-process-management-v3
                - postgres-production-core-short-for-test-v3
                - postgres-production-pln-team-long-life-v3
                - postgres-production-crm-long-life-v3
                - postgres-production-crew-ai-seo-automation-long-life-v3
                - postgres-production-ops-service-long-life-v3
              username: "${env `POSTGRES_MAIN_USER_PRODUCTION_V3`}"
              password: "${env `POSTGRES_MAIN_PASSWORD_PRODUCTION_V3`}"

            - name: postgres-production-mls-config-v3
              plugin_name: "postgresql-database-plugin"
              connection_url: "postgresql://{{username}}:{{password}}@${env `POSTGRES_MLS_HOST_PRODUCTION_V3`}:5432/postgres"
              allowed_roles:
                - postgres-production-search-long-life-v3
              username: "${env `POSTGRES_MLS_USER_PRODUCTION_V3`}"
              password: "${env `POSTGRES_MLS_PASSWORD_PRODUCTION_V3`}"

            - name: postgres-staging-mls-config-v4
              plugin_name: "postgresql-database-plugin"
              connection_url: "postgresql://{{username}}:{{password}}@${env `POSTGRES_MLS_HOST_STAGING_V4`}:5432/postgres"
              allowed_roles:
                - postgres-staging-search-long-life-v4
                - postgres-staging-testvaultlambda-long-life-v4
              username: "${env `POSTGRES_MLS_USER_STAGING_V4`}"
              password: "${env `POSTGRES_MLS_PASSWORD_STAGING_V4`}"
          roles:
            # Staging V4
            - name: postgres-staging-all_main-dev-v4
              db_name: postgres-staging-main-config-v4
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_all ADD USER \"{{name}}\";"
              default_ttl: 168h # 7 days
              max_ttl: 720h
            - name: postgres-staging-website-long-life-v4
              db_name: postgres-staging-main-config-v4
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_website ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h
            - name: postgres-staging-property-long-life-v4
              db_name: postgres-staging-main-config-v4
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_property ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h
            - name: postgres-staging-slide-long-life-v4
              db_name: postgres-staging-main-config-v4
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_slide ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h
            - name: postgres-staging-crm-long-life-v4
              db_name: postgres-staging-main-config-v4
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_crm ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h
            - name: postgres-staging-ai-agents-long-life-v4
              db_name: postgres-staging-main-config-v4
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_ai_agents ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h
            - name: postgres-staging-network-long-life-v4
              db_name: postgres-staging-main-config-v4
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_network ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h
            - name: postgres-staging-notification-long-life-v4
              db_name: postgres-staging-main-config-v4
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_notification ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h
            - name: postgres-staging-core-long-life-v4
              db_name: postgres-staging-main-config-v4
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_core ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h
            - name: postgres-staging-identity-long-life-v4
              db_name: postgres-staging-main-config-v4
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_identity ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h
            - name: postgres-staging-buyerseller-long-life-v4
              db_name: postgres-staging-main-config-v4
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_buyerseller ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h
            - name: postgres-staging-search-long-life-v4
              db_name: postgres-staging-mls-config-v4
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_search ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h
            - name: postgres-staging-crew-ai-seo-automation-long-life-v4
              db_name: postgres-staging-main-config-v4
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_crew_ai_seo_automation ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h
            - name: postgres-staging-ops-service-long-life-v4
              db_name: postgres-staging-main-config-v4
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_ops_service ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h
            - name: postgres-staging-testvaultlambda-long-life-v4
              db_name: postgres-staging-mls-config-v4
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_search ADD USER \"{{name}}\";"
              default_ttl: 1m
              max_ttl: 72h

            # Production V3 roles
            - name: postgres-production-notification-long-life-v3
              db_name: postgres-production-main-config-v3
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_notification ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h

            - name: postgres-production-core-long-life-v3
              db_name: postgres-production-main-config-v3
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_core ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h

            - name: postgres-production-core-process-management-v3
              db_name: postgres-production-main-config-v3
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_core ADD USER \"{{name}}\"; GRANT pg_monitor TO \"{{name}}\"; GRANT pg_signal_backend TO \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h

            - name: postgres-production-core-short-for-test-v3
              db_name: postgres-production-main-config-v3
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_core ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h

            - name: postgres-production-network-long-life-v3
              db_name: postgres-production-main-config-v3
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_network ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h

            - name: postgres-production-property-long-life-v3
              db_name: postgres-production-main-config-v3
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_property ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h

            - name: postgres-production-slide-long-life-v3
              db_name: postgres-production-main-config-v3
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_slide ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h

            - name: postgres-production-website-long-life-v3
              db_name: postgres-production-main-config-v3
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_website ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h

            - name: postgres-production-buyerseller-long-life-v3
              db_name: postgres-production-main-config-v3
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_buyerseller ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h

            - name: postgres-production-identity-long-life-v3
              db_name: postgres-production-main-config-v3
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_identity ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h

            - name: postgres-production-pln-team-long-life-v3
              db_name: postgres-production-main-config-v3
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_pln_team ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h

            - name: postgres-production-crm-long-life-v3
              db_name: postgres-production-main-config-v3
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_crm ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h

            - name: postgres-production-search-long-life-v3
              db_name: postgres-production-mls-config-v3
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_search ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h

            - name: postgres-production-crew-ai-seo-automation-long-life-v3
              db_name: postgres-production-main-config-v3
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_crew_ai_seo_automation ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h

            - name: postgres-production-ops-service-long-life-v3
              db_name: postgres-production-main-config-v3
              creation_statements: "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}'; ALTER GROUP admin_ops_service ADD USER \"{{name}}\";"
              default_ttl: 168h
              max_ttl: 720h

  istioEnabled: false
  tolerations:
    - key: "node-group-name"
      operator: "Equal"
      value: "vault"
      effect: "NoSchedule"
  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
        - weight: 100
          podAffinityTerm:
            labelSelector:
              matchLabels:
                app.kubernetes.io/name: vault
                vault_cr: vault
            topologyKey: failure-domain.beta.kubernetes.io/zone
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: node-group-name
                operator: In
                values:
                  - vault
  envsConfig:
    - name: POSTGRES_MAIN_PASSWORD_STAGING_V4
      valueFrom:
        secretKeyRef:
          name: staging-main-db-secrets-v4
          key: PASSWORD
    - name: POSTGRES_MAIN_USER_STAGING_V4
      valueFrom:
        secretKeyRef:
          name: staging-main-db-secrets-v4
          key: USER
    - name: POSTGRES_MAIN_HOST_STAGING_V4
      valueFrom:
        secretKeyRef:
          name: staging-main-db-secrets-v4
          key: HOST

    - name: POSTGRES_MAIN_PASSWORD_PRODUCTION_V3
      valueFrom:
        secretKeyRef:
          name: production-main-db-secrets-v3
          key: PASSWORD
    - name: POSTGRES_MAIN_USER_PRODUCTION_V3
      valueFrom:
        secretKeyRef:
          name: production-main-db-secrets-v3
          key: USER
    - name: POSTGRES_MAIN_HOST_PRODUCTION_V3
      valueFrom:
        secretKeyRef:
          name: production-main-db-secrets-v3
          key: HOST

    - name: POSTGRES_MLS_PASSWORD_PRODUCTION_V3
      valueFrom:
        secretKeyRef:
          name: production-mls-db-secrets-v3
          key: PASSWORD
    - name: POSTGRES_MLS_USER_PRODUCTION_V3
      valueFrom:
        secretKeyRef:
          name: production-mls-db-secrets-v3
          key: USER
    - name: POSTGRES_MLS_HOST_PRODUCTION_V3
      valueFrom:
        secretKeyRef:
          name: production-mls-db-secrets-v3
          key: HOST

    - name: POSTGRES_MLS_PASSWORD_STAGING_V4
      valueFrom:
        secretKeyRef:
          name: staging-mls-db-secrets-v4
          key: PASSWORD
    - name: POSTGRES_MLS_USER_STAGING_V4
      valueFrom:
        secretKeyRef:
          name: staging-mls-db-secrets-v4
          key: USER
    - name: POSTGRES_MLS_HOST_STAGING_V4
      valueFrom:
        secretKeyRef:
          name: staging-mls-db-secrets-v4
          key: HOST

    - name: AUTH0_CLIENT_ID
      valueFrom:
        secretKeyRef:
          name: oidc-auth0-operation
          key: AUTH0_CLIENT_ID
    - name: AUTH0_CLIENT_SECRET
      valueFrom:
        secretKeyRef:
          name: oidc-auth0-operation
          key: AUTH0_CLIENT_SECRET
    - name: AUTH0_DOMAIN
      valueFrom:
        secretKeyRef:
          name: oidc-auth0-operation
          key: AUTH0_DOMAIN

    - name: AWS_STAGING_ACCESS_KEY
      valueFrom:
        secretKeyRef:
          name: staging-aws-secrets
          key: AWS_STAGING_ACCESS_KEY
    - name: AWS_STAGING_SECRET_KEY
      valueFrom:
        secretKeyRef:
          name: staging-aws-secrets
          key: AWS_STAGING_SECRET_KEY
