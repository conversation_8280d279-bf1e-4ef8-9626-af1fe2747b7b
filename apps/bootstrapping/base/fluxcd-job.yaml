apiVersion: batch/v1
kind: Job
metadata:
  name: installer-fluxcd
  namespace: flux-system
  annotations:
    argocd.argoproj.io/hook: PreSync
    argocd.argoproj.io/hook-delete-policy: BeforeHookCreation
spec:
  template:
    spec:
      serviceAccountName: installer-fluxcd-sa
      containers:
        - name: installer-fluxcd
          image: docker.io/fluxcd/flux-cli:v2.0.1
          env:
            - name: GITHUB_TOKEN
              valueFrom:
                secretKeyRef:
                  name: github-token
                  key: token
          command: ["/bin/sh","-c"]
          args:
            - |
              echo "Bootstrapping cluster with flux"
              server="https://$KUBERNETES_SERVICE_HOST:$KUBERNETES_SERVICE_PORT"
              sa_token_name=$(kubectl -n flux-system get sa installer-fluxcd-sa -o jsonpath='{.secrets[0].name}')
              ca=$(kubectl -n flux-system get secret $sa_token_name -o jsonpath="{.data['ca\.crt']}")
              sa_token=$(kubectl -n flux-system get secret $sa_token_name -o jsonpath="{.data.token}" | base64 -d)
              cd /tmp
              touch kubeconfig
              echo "
              apiVersion: v1
              kind: Config
              clusters:
              - name: default-cluster
                cluster:
                  certificate-authority-data: ${ca}
                  server: ${server}
              contexts:
              - name: default-context
                context:
                  cluster: default-cluster
                  namespace: flux-system
                  user: default-user
              current-context: default-context
              users:
              - name: default-user
                user:
                  token: ${sa_token}
              " > kubeconfig
              echo "Bootstrapping cluster with flux"
              flux bootstrap github --owner=luxurypresence --repository=apps-k8s-config --path=flux/clusters/operations --branch=main --components-extra=image-reflector-controller,image-automation-controller --kubeconfig kubeconfig --ssh-key-algorithm ecdsa --read-write-key true
      restartPolicy: OnFailure
