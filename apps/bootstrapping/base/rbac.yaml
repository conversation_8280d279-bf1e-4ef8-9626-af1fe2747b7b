apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: fluxcd-installer-cr
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: installer-fluxcd-crn
subjects:
  - kind: ServiceAccount
    name: installer-fluxcd-sa
    namespace: flux-system
roleRef:
  kind: ClusterRole
  name: fluxcd-installer-cr
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: installer-fluxcd-sa
  namespace: flux-system
