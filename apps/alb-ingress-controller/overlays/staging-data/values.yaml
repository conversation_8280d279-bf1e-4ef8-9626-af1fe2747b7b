clusterName: staging_eks_data1
nodeSelector:
  karpenter.sh/nodepool: on-demand-base
tolerations:
  - key: "node-type"
    operator: "Equal"
    value: "on-demand"
    effect: "NoSchedule"
serviceAccount:
  create: true
  name: alb-ingress-controller
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/alb-ingress-controller-role
enableServiceMutatorWebhook: false
vpcId: vpc-06e4165b4af3f1edc
region: us-east-1
