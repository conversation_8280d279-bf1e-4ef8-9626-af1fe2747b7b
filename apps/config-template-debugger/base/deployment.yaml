apiVersion: apps/v1
kind: Deployment
metadata:
  name: config-template-debugger
  labels:
    app.kubernetes.io/name: config-template-debugger
spec:
  replicas: 2
  selector:
    matchLabels:
      app.kubernetes.io/name: config-template-debugger
      app.kubernetes.io/instance: config-template-debugger
  template:
    metadata:
      labels:
        app.kubernetes.io/name: config-template-debugger
        app.kubernetes.io/instance: config-template-debugger
    spec:
      serviceAccountName: config-template-debugger
      containers:
        - name: config-template-debugger
          image: "luxurypresence/config-template-debugger:latest"
          imagePullPolicy: Always
          command: ["npm"]
          args: ["start"]
          resources:
            limits:
              cpu: "2"
              memory: "3Gi"
            requests:
              cpu: "1"
              memory: "2Gi"
