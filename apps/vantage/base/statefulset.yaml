# Source: vantage-kubernetes-agent/templates/application.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: vka-vantage-kubernetes-agent
  namespace: vantage
  labels:
    helm.sh/chart: vantage-kubernetes-agent-1.1.2
    app.kubernetes.io/name: vantage-kubernetes-agent
    app.kubernetes.io/instance: vka
    app.kubernetes.io/version: "1.0.28"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  serviceName: vantage-agent
  selector:
    matchLabels:
      app.kubernetes.io/name: vantage-kubernetes-agent
      app.kubernetes.io/instance: vka
  template:
    metadata:
      labels:
        app.kubernetes.io/name: vantage-kubernetes-agent
        app.kubernetes.io/instance: vka
    spec:
      serviceAccountName: vka-vantage-kubernetes-agent
      containers:
        - name: vantage-kubernetes-agent
          securityContext:
            {}
          image: "quay.io/vantage-sh/kubernetes-agent:1.0.28"
          imagePullPolicy: IfNotPresent
          env:
            - name: VANTAGE_DEBUG
              value: "false"
            - name: VANTAGE_LOG_LEVEL
              value: "0"
            - name: VANTAGE_CLUSTER_ID
              value: "cluster-placeholder"
            - name: VANTAGE_COLLECT_NAMESPACE_LABELS
              value: "false"
            - name: VANTAGE_API_TOKEN
              valueFrom:
                secretKeyRef:
                  name: vantage-token-secret
                  key: token
            - name: VANTAGE_PERSIST_DIR
              value: "/var/lib/vantage-agent"
            - name: VANTAGE_POLLING_INTERVAL
              value: "60"
          ports:
            - name: report
              containerPort: 9010
              protocol: TCP
          readinessProbe:
            httpGet:
              path: /healthz
              port: report
          resources:
            limits:
              cpu: "1"
              memory: "1Gi"
            requests:
              cpu: "0.5"
              memory: "0.5Gi"
          volumeMounts:
          - name: data
            mountPath: /var/lib/vantage-agent
      volumes: []
      securityContext:
        {}
  volumeClaimTemplates:
  - metadata:
      name: data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: gp3
      resources:
        requests:
          storage: 10Gi
