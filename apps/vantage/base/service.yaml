# Source: vantage-kubernetes-agent/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: vka-vantage-kubernetes-agent
  namespace: vantage
  labels:
    helm.sh/chart: vantage-kubernetes-agent-1.1.2
    app.kubernetes.io/name: vantage-kubernetes-agent
    app.kubernetes.io/instance: vka
    app.kubernetes.io/version: "1.0.28"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
    - port: 9010
      targetPort: report
      protocol: TCP
      name: report
  selector:
    app.kubernetes.io/name: vantage-kubernetes-agent
    app.kubernetes.io/instance: vka
