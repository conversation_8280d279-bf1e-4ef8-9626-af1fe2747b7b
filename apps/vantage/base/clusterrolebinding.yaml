# Source: vantage-kubernetes-agent/templates/clusterrolebinding.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: vka-vantage-kubernetes-agent
  labels:
    helm.sh/chart: vantage-kubernetes-agent-1.1.2
    app.kubernetes.io/name: vantage-kubernetes-agent
    app.kubernetes.io/instance: vka
    app.kubernetes.io/version: "1.0.28"
    app.kubernetes.io/managed-by: Helm
subjects:
- kind: ServiceAccount
  name: vka-vantage-kubernetes-agent
  namespace: vantage
roleRef:
  kind: ClusterRole
  name: vka-vantage-kubernetes-agent
  apiGroup: rbac.authorization.k8s.io
