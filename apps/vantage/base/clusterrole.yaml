# Source: vantage-kubernetes-agent/templates/clusterrole.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: vka-vantage-kubernetes-agent
  labels:
    helm.sh/chart: vantage-kubernetes-agent-1.1.2
    app.kubernetes.io/name: vantage-kubernetes-agent
    app.kubernetes.io/instance: vka
    app.kubernetes.io/version: "1.0.28"
    app.kubernetes.io/managed-by: Helm
rules:
- apiGroups: [""]
  resources: ["nodes/metrics"]
  verbs: ["get"]
- apiGroups: [""]
  resources:
  - "nodes"
  - "pods"
  - "namespaces"
  - "replicationcontrollers"
  - "persistentvolumes"
  - "persistentvolumeclaims"
  verbs: ["get", "watch", "list"]
- apiGroups: ["apps"]
  resources:
  - "replicasets"
  - "deployments"
  - "statefulsets"
  - "daemonsets"
  verbs: ["get", "watch", "list"]
- apiGroups: ["batch"]
  resources:
  - "jobs"
  - "cronjobs"
  verbs: ["get", "watch", "list"]
