apiVersion: operator.victoriametrics.com/v1beta1
kind: VMServiceScrape
metadata:
  name: starrocks-metrics-annotations
  namespace: monitoring
  labels:
    app.kubernetes.io/name: starrocks-annotations
    app.kubernetes.io/component: metrics
spec:
  # Target the starrocks namespace
  namespaceSelector:
    matchNames:
      - starrocks
  
  # Select services with prometheus scraping annotations
  selector:
    matchExpressions:
      - key: prometheus.io/scrape
        operator: In
        values: ["true"]
  
  # Single endpoint that uses annotations to determine scraping config
  endpoints:
    - interval: 30s
      scrapeTimeout: 10s
      honorLabels: true
      # Relabel configs to use prometheus annotations
      relabelConfigs:
        # Use prometheus.io/path annotation for metrics path
        - sourceLabels: [__meta_kubernetes_service_annotation_prometheus_io_path]
          targetLabel: __metrics_path__
          regex: (.+)
        
        # Use prometheus.io/port annotation for port
        - sourceLabels: [__address__, __meta_kubernetes_service_annotation_prometheus_io_port]
          targetLabel: __address__
          regex: ([^:]+)(?::\d+)?;(\d+)
          replacement: $1:$2
        
        # Add service name as job label
        - sourceLabels: [__meta_kubernetes_service_name]
          targetLabel: job
          replacement: starrocks-${1}
        
        # Add component label based on service name
        - sourceLabels: [__meta_kubernetes_service_name]
          targetLabel: component
          regex: .*-fe.*
          replacement: frontend
        
        - sourceLabels: [__meta_kubernetes_service_name]
          targetLabel: component
          regex: .*-cn.*
          replacement: compute-node
        
        - sourceLabels: [__meta_kubernetes_service_name]
          targetLabel: component
          regex: .*-be.*
          replacement: backend
        
        # Add namespace label
        - sourceLabels: [__meta_kubernetes_namespace]
          targetLabel: kubernetes_namespace
        
        # Add pod name
        - sourceLabels: [__meta_kubernetes_pod_name]
          targetLabel: kubernetes_pod_name
      
      # Metric relabel configs to clean up metrics
      metricRelabelConfigs:
        # Keep only StarRocks metrics and some standard metrics
        - sourceLabels: [__name__]
          regex: 'starrocks_.*|up|scrape_.*'
          action: keep
        
        # Add cluster label
        - targetLabel: cluster
          replacement: lp-staging
        
        # Add environment label  
        - targetLabel: environment
          replacement: staging

  # Use service name as job label
  jobLabel: __meta_kubernetes_service_name
