apiVersion: operator.victoriametrics.com/v1beta1
kind: VMServiceScrape
metadata:
  name: starrocks-nodeinfo
  namespace: monitoring
  labels:
    app.kubernetes.io/name: starrocks-nodeinfo
spec:
  # Target the starrocks namespace
  namespaceSelector:
    matchNames:
      - starrocks
  
  # Select all services in starrocks namespace
  selector: {}
  
  # Define endpoints for node_info metrics (usually on admin ports)
  endpoints:
    # Frontend admin port for node_info
    - port: "8030"
      path: /metrics
      interval: 30s
      scrapeTimeout: 10s
      honorLabels: true
      relabelConfigs:
        # Only scrape services with prometheus.io/scrape=true annotation
        - sourceLabels: [__meta_kubernetes_service_annotation_prometheus_io_scrape]
          regex: "true"
          action: keep
        
        # Only scrape if port matches 8030 (FE admin)
        - sourceLabels: [__meta_kubernetes_service_annotation_prometheus_io_port]
          regex: "8030"
          action: keep
        
        # Set job name to cluster name
        - targetLabel: job
          replacement: kube-starrocks
        
        # Set group label for FE
        - targetLabel: group
          replacement: fe
        
        # Add service and namespace labels
        - sourceLabels: [__meta_kubernetes_service_name]
          targetLabel: service
        - sourceLabels: [__meta_kubernetes_namespace]
          targetLabel: namespace
      
      metricRelabelConfigs:
        # Keep only node_info and basic metrics
        - sourceLabels: [__name__]
          regex: 'node_info|up'
          action: keep
        
        # Add cluster label
        - targetLabel: cluster
          replacement: lp-staging
        - targetLabel: environment
          replacement: staging
    
    # Backend admin port for node_info  
    - port: "8040"
      path: /metrics
      interval: 30s
      scrapeTimeout: 10s
      honorLabels: true
      relabelConfigs:
        # Only scrape services with prometheus.io/scrape=true annotation
        - sourceLabels: [__meta_kubernetes_service_annotation_prometheus_io_scrape]
          regex: "true"
          action: keep
        
        # Only scrape if port matches 8040 (BE/CN admin)
        - sourceLabels: [__meta_kubernetes_service_annotation_prometheus_io_port]
          regex: "8040"
          action: keep
        
        # Set job name to cluster name
        - targetLabel: job
          replacement: kube-starrocks
        
        # Set group label based on service name
        - sourceLabels: [__meta_kubernetes_service_name]
          targetLabel: group
          regex: ".*-cn.*"
          replacement: cn
        
        - sourceLabels: [__meta_kubernetes_service_name]
          targetLabel: group
          regex: ".*-be.*"
          replacement: be
        
        # Add service and namespace labels
        - sourceLabels: [__meta_kubernetes_service_name]
          targetLabel: service
        - sourceLabels: [__meta_kubernetes_namespace]
          targetLabel: namespace
      
      metricRelabelConfigs:
        # Keep only node_info and basic metrics
        - sourceLabels: [__name__]
          regex: 'node_info|up'
          action: keep
        
        # Add cluster label
        - targetLabel: cluster
          replacement: lp-staging
        - targetLabel: environment
          replacement: staging
