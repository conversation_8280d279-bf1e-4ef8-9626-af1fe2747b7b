apiVersion: operator.victoriametrics.com/v1beta1
kind: VMServiceScrape
metadata:
  name: starrocks-simple
  namespace: monitoring
  labels:
    app.kubernetes.io/name: starrocks-simple
spec:
  # Target the starrocks namespace
  namespaceSelector:
    matchNames:
      - starrocks

  # Select all services in starrocks namespace (we'll filter with relabeling)
  selector: {}

  # Define endpoints for StarRocks metrics
  endpoints:
    # Frontend (FE) metrics - port 8030
    - port: "8030"
      path: /metrics
      interval: 30s
      scrapeTimeout: 10s
      honorLabels: true
      relabelConfigs:
        # Only scrape services with prometheus.io/scrape=true annotation
        - sourceLabels: [__meta_kubernetes_service_annotation_prometheus_io_scrape]
          regex: "true"
          action: keep

        # Only scrape if port matches 8030 (FE)
        - sourceLabels: [__meta_kubernetes_service_annotation_prometheus_io_port]
          regex: "8030"
          action: keep

        # Set job name to cluster name (expected by dashboard)
        - targetLabel: job
          replacement: kube-starrocks

        # Set group label (required by dashboard)
        - targetLabel: group
          replacement: fe

        # Set component label
        - targetLabel: component
          replacement: frontend

        # Add service and namespace labels
        - sourceLabels: [__meta_kubernetes_service_name]
          targetLabel: service
        - sourceLabels: [__meta_kubernetes_namespace]
          targetLabel: namespace

      metricRelabelConfigs:
        # Keep all relevant metrics for the dashboard
        - sourceLabels: [__name__]
          regex: 'starrocks_.*|jvm_.*|node_info|up|process_.*'
          action: keep

        # Add cluster label
        - targetLabel: cluster
          replacement: lp-staging
        - targetLabel: environment
          replacement: staging

    # Compute Node (CN) and Backend (BE) metrics - port 8040
    - port: "8040"
      path: /metrics
      interval: 30s
      scrapeTimeout: 10s
      honorLabels: true
      relabelConfigs:
        # Only scrape services with prometheus.io/scrape=true annotation
        - sourceLabels: [__meta_kubernetes_service_annotation_prometheus_io_scrape]
          regex: "true"
          action: keep

        # Only scrape if port matches 8040 (CN/BE)
        - sourceLabels: [__meta_kubernetes_service_annotation_prometheus_io_port]
          regex: "8040"
          action: keep

        # Set job name to cluster name (expected by dashboard)
        - targetLabel: job
          replacement: kube-starrocks

        # Set group label based on service name (required by dashboard)
        - sourceLabels: [__meta_kubernetes_service_name]
          targetLabel: group
          regex: ".*-cn.*"
          replacement: cn

        - sourceLabels: [__meta_kubernetes_service_name]
          targetLabel: group
          regex: ".*-be.*"
          replacement: be

        # Set component label based on service name
        - sourceLabels: [__meta_kubernetes_service_name]
          targetLabel: component
          regex: ".*-cn.*"
          replacement: compute-node

        - sourceLabels: [__meta_kubernetes_service_name]
          targetLabel: component
          regex: ".*-be.*"
          replacement: backend

        # Add service and namespace labels
        - sourceLabels: [__meta_kubernetes_service_name]
          targetLabel: service
        - sourceLabels: [__meta_kubernetes_namespace]
          targetLabel: namespace

      metricRelabelConfigs:
        # Keep all relevant metrics for the dashboard
        - sourceLabels: [__name__]
          regex: 'starrocks_.*|jvm_.*|node_info|up|process_.*'
          action: keep

        # Add cluster label
        - targetLabel: cluster
          replacement: lp-staging
        - targetLabel: environment
          replacement: staging
