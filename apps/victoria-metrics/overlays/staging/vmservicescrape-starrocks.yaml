apiVersion: operator.victoriametrics.com/v1beta1
kind: VMServiceScrape
metadata:
  name: starrocks-metrics
  namespace: monitoring
  labels:
    app.kubernetes.io/name: starrocks
    app.kubernetes.io/component: metrics
spec:
  # Target the starrocks namespace
  namespaceSelector:
    matchNames:
      - starrocks
  
  # Select StarRocks services with prometheus scraping annotations
  selector:
    matchExpressions:
      - key: prometheus.io/scrape
        operator: In
        values: ["true"]
      - key: app.kubernetes.io/name
        operator: In
        values: ["kube-starrocks"]
  
  # Define endpoints for different StarRocks components
  endpoints:
    # Frontend (FE) metrics endpoint
    - port: http
      path: /metrics
      interval: 30s
      scrapeTimeout: 10s
      honorLabels: true
      metricRelabelConfigs:
        - sourceLabels: [__name__]
          regex: 'starrocks_.*'
          action: keep
        - sourceLabels: [__meta_kubernetes_service_annotation_prometheus_io_port]
          regex: '8030'
          action: keep
          targetLabel: __tmp_port_fe
    
    # Compute Node (CN) metrics endpoint  
    - port: webserver
      path: /metrics
      interval: 30s
      scrapeTimeout: 10s
      honorLabels: true
      metricRelabelConfigs:
        - sourceLabels: [__name__]
          regex: 'starrocks_.*'
          action: keep
        - sourceLabels: [__meta_kubernetes_service_annotation_prometheus_io_port]
          regex: '8040'
          action: keep
          targetLabel: __tmp_port_cn
    
    # Backend (BE) metrics endpoint
    - port: webserver
      path: /metrics  
      interval: 30s
      scrapeTimeout: 10s
      honorLabels: true
      metricRelabelConfigs:
        - sourceLabels: [__name__]
          regex: 'starrocks_.*'
          action: keep
        - sourceLabels: [__meta_kubernetes_service_annotation_prometheus_io_port]
          regex: '8040'
          action: keep
          targetLabel: __tmp_port_be

  # Add job labels for better organization
  jobLabel: app.kubernetes.io/component
