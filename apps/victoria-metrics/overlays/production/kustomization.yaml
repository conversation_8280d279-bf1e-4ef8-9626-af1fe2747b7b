apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

helmCharts:
  - name: victoria-metrics-k8s-stack
    namespace: monitoring
    repo: https://victoriametrics.github.io/helm-charts/
    releaseName: victoria-metrics-k8s-stack
    version: 0.51.0
    valuesFile: values.yaml
    includeCRDs: true

# Note: Grafana is deployed via pre-templated manifests instead of Helm chart
# to avoid PodDisruptionBudget API version compatibility issues with Kubernetes 1.25+
