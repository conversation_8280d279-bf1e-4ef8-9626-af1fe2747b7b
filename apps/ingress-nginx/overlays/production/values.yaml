controller:
  ingressClassByName: true
  allowSnippetAnnotations: true
  service:
    annotations:
      service.beta.kubernetes.io/aws-load-balancer-ssl-cert: "arn:aws:acm:us-east-1:381475384502:certificate/6bdc60fc-dd5b-447c-a75e-d719e32d361f"
      service.beta.kubernetes.io/aws-load-balancer-backend-protocol: tcp
      service.beta.kubernetes.io/aws-load-balancer-proxy-protocol: "*"
      service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: 'true'
      service.beta.kubernetes.io/aws-load-balancer-type: nlb
      service.beta.kubernetes.io/aws-load-balancer-ssl-ports: https
      service.beta.kubernetes.io/aws-load-balancer-connection-idle-timeout: '3600'
      service.beta.kubernetes.io/aws-load-balancer-ssl-negotiation-policy: "ELBSecurityPolicy-TLS13-1-2-2021-06"
      service.beta.kubernetes.io/aws-load-balancer-name: "ingres-nginx-production"
    targetPorts:
      http: http
      https: special
    externalTrafficPolicy: "Local"
  containerPort:
    http: 80
    https: 443
    special: 8000
  config:
    real-ip-header: "proxy_protocol"
    set-real-ip-from: "0.0.0.0/0"
    proxy-read-timeout: "3600"
    proxy-send-timeout: "3600"
    use-forwarded-headers: true
    ssl-redirect: "false" # we use `special` port to control ssl redirection
    server-snippet: |
      listen 8000;
      if ( $server_port = 80 ) {
        return 308 https://$host$request_uri;
      }
  resources:
    limits:
      cpu: 500m
      memory: 400Mi
    requests:
      cpu: 200m
      memory: 300Mi
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 3
    targetCPUUtilizationPercentage: 60
    targetMemoryUtilizationPercentage: 70
  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
        - weight: 100
          podAffinityTerm:
            labelSelector:
              matchExpressions:
                - key: app.kubernetes.io/instance
                  operator: In
                  values:
                    - ingress-nginx
            topologyKey: kubernetes.io/hostname
  extraInitContainers:
    - name: patch-configmap
      image: bitnamilegacy/kubectl:1.27.1
      imagePullPolicy: IfNotPresent
      env:
      - name: API_KEY_TENANT_SERVICE
        valueFrom:
          secretKeyRef:
            name: ingress-nginx
            key: API_KEY_TENANT_SERVICE
      - name: API_KEY_CMS_SERVICE
        valueFrom:
          secretKeyRef:
            name: ingress-nginx
            key: API_KEY_CMS_SERVICE
      - name: API_KEY_CRM_SERVICE
        valueFrom:
          secretKeyRef:
            name: ingress-nginx
            key: API_KEY_CRM_SERVICE
      command: ["/bin/sh","-c"]
      args:
        - |
          echo "Creating patch file"
          echo "
          data:
            http-snippet: |
              map \$http_x_api_key \$apikey_is_ok {
                ${API_KEY_TENANT_SERVICE}  tenant-key;
                ${API_KEY_CMS_SERVICE}  cms-key;
                ${API_KEY_CRM_SERVICE}  crm-key;
              }
          " > /tmp/patch-cm.yaml
          echo "Patching ingress-nginx configmap"
          kubectl -n ingress-nginx patch configmap ingress-nginx-controller --patch-file /tmp/patch-cm.yaml
