apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: ingress-nginx
  namespace: ingress-nginx-staging
spec:
  refreshInterval: "60s"
  secretStoreRef:
    name: ingress-nginx
    kind: ClusterSecretStore
  target:
    name: ingress-nginx
  data:
    - secretKey: API_KEY_TENANT_SERVICE
      remoteRef:
        key: staging/tenant-service
        property: NGINX_API_KEY
    - secretKey: API_KEY_CMS_SERVICE
      remoteRef:
        key: staging/cms-service
        property: NGINX_API_KEY
    - secretKey: API_KEY_CRM_SERVICE
      remoteRef:
        key: staging/crm-service
        property: NGINX_API_KEY
