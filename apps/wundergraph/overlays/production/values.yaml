existingSecret: "wundergraph-secrets"

image:
  version: "0.243.0"

ingress:
  enabled: true
  annotations:
    alb.ingress.kubernetes.io/group.name: global-ingress
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/healthcheck-path: /health/live
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
  hosts:
    - host: graphql.luxurypresence.com
      paths:
        - path: /*
          pathType: ImplementationSpecific

autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 15
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

podAnnotations:
  ad.datadoghq.com/scrape: 'true'
  ad.datadoghq.com/router.checks: |
    {
      "openmetrics": {
        "init_config": {},
        "instances": [
          {
            "openmetrics_endpoint": "http://%%host%%:8088/metrics",
            "namespace": "wundergraph",
            "metrics": [
              "router_*",
              "go_goroutines",
              "go_memstats_alloc_bytes",
              "go_memstats_heap_alloc_bytes",
              "go_memstats_heap_idle_bytes",
              "go_memstats_heap_sys_bytes",
              "go_memstats_sys_bytes",
              "go_memstats_frees_total"
            ]
          }
        ]
      }
    }

resources:
  limits:
    cpu: 300m
    memory: 256Mi
  requests:
    cpu: 300m
    memory: 256Mi

affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchExpressions:
              - key: app.kubernetes.io/name
                operator: In
                values:
                  - router
          topologyKey: kubernetes.io/hostname


commonConfiguration: |-
  version: "1"
  log_level: "info"
  cors:
    allow_methods:
      - "POST"
      - "GET"
      - "OPTIONS"
    allow_origins:
      - https://app.luxurypresence.com
      - https://graphql.luxurypresence.com
    allow_headers:
      - "Authorization"
      - "Content-Type"
      - "Origin"
    allow_credentials: true
    max_age: 5m
  headers:
    all:
      request:
        - op: "propagate"
          named: cookie
        - op: "propagate"
          named: Authorization
        - op: "propagate"
          named: x-lp-api-key
        - op: "propagate"
          named: x-utm-vars
        - op: "propagate"
          named: x-referrer
        - op: "propagate"
          named: x-forwarded-host
        - op: "propagate"
          named: x-lp-internalhost
  telemetry:
    service_name: "wundergraph"
    tracing:
      enabled: true
    metrics:
      prometheus:
        exclude_metric_labels:
          - "^wg_client_version$"
          - "^wg_client_name$"
          - "^wg_operation_protocol$"
          - "^wg.router.version$"
          - "^wg.router.config.version$"
  subgraph_error_propagation:
    enabled: true
    propagate_status_codes: true
    mode: "pass-through"
