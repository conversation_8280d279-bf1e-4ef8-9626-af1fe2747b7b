apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: starrocks-m7g-fe
spec:
  template:
    metadata:
      labels:
        nodepool-role: starrocks-m7g-fe
        node-group-name: starrocks-m7g-fe
        workload-type: starrocks-fe
    spec:
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: starrocks
      taints:
        - key: node-group-role
          value: "starrocks-m7g-fe"
          effect: NoSchedule
        - key: workload-type
          value: "starrocks-fe"
          effect: NoSchedule
      requirements:
        - key: "karpenter.k8s.aws/instance-family"
          operator: In
          values: ["m7g"]
        - key: "karpenter.k8s.aws/instance-size"
          operator: In
          values: ["2xlarge"]
        - key: "karpenter.k8s.aws/instance-hypervisor"
          operator: In
          values: ["nitro"]
        - key: "karpenter.sh/capacity-type"
          operator: In
          values: ["on-demand"]
        - key: kubernetes.io/arch
          operator: In
          values: ["arm64"]
      expireAfter: Never
  limits:
    cpu: 200
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 30s

---
apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: starrocks-r7g-be
spec:
  template:
    metadata:
      labels:
        nodepool-role: starrocks-r7g-be
        node-group-name: starrocks-r7g-be
        workload-type: starrocks-be
    spec:
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: starrocks
      taints:
        - key: node-group-role
          value: "starrocks-r7g-be"
          effect: NoSchedule
        - key: workload-type
          value: "starrocks-be"
          effect: NoSchedule
      requirements:
        - key: "karpenter.k8s.aws/instance-family"
          operator: In
          values: ["r7g"]
        - key: "karpenter.k8s.aws/instance-size"
          operator: In
          values: ["4xlarge"]
        - key: "karpenter.k8s.aws/instance-hypervisor"
          operator: In
          values: ["nitro"]
        - key: "karpenter.sh/capacity-type"
          operator: In
          values: ["on-demand"]
        - key: kubernetes.io/arch
          operator: In
          values: ["arm64"]
      expireAfter: Never
  limits:
    cpu: 1000
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 30s

---
apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: starrocks-c7g-cn
spec:
  template:
    metadata:
      labels:
        nodepool-role: starrocks-c7g-cn
        node-group-name: starrocks-c7g-cn
        workload-type: starrocks-cn
    spec:
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: starrocks
      taints:
        - key: node-group-role
          value: "starrocks-c7g-cn"
          effect: NoSchedule
        - key: workload-type
          value: "starrocks-cn"
          effect: NoSchedule
      requirements:
        - key: "karpenter.k8s.aws/instance-family"
          operator: In
          values: ["c7g"]
        - key: "karpenter.k8s.aws/instance-size"
          operator: In
          values: ["2xlarge"]
        - key: "karpenter.k8s.aws/instance-hypervisor"
          operator: In
          values: ["nitro"]
        - key: "karpenter.sh/capacity-type"
          operator: In
          values: ["on-demand"]
        - key: kubernetes.io/arch
          operator: In
          values: ["arm64"]
      expireAfter: Never
  limits:
    cpu: 200
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 30s
