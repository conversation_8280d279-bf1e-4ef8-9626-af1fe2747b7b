apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: starrocks
spec:
  amiFamily: AL2
  amiSelectorTerms:
    - alias: al2@latest
  role: Karpenter-production_eks1
  subnetSelectorTerms:
    - tags:
        Type: private-vpc1
  securityGroupSelectorTerms:
    - tags:
        karpenter.sh/discovery: production_eks1
  tags:
    karpenter.sh/discovery: production_eks1
  blockDeviceMappings:
    - deviceName: /dev/xvda
      ebs:
        volumeSize: 150Gi
        volumeType: gp3
        encrypted: true
        deleteOnTermination: true
