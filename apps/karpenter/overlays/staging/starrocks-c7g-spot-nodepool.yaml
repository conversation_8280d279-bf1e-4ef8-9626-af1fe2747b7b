apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: starrocks-c7g-spot
spec:
  template:
    metadata:
      labels:
        nodepool-role: starrocks-c7g-spot
        node-group-name: starrocks-c7g-spot
        workload-type: starrocks
    spec:
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: starrocks-c7g-spot
      taints:
        - key: node-group-role
          value: "starrocks-c7g-spot"
          effect: NoSchedule
        - key: workload-type
          value: "starrocks"
          effect: NoSchedule
      requirements:
        - key: "karpenter.k8s.aws/instance-family"
          operator: In
          values: ["c7g"]
        - key: "karpenter.k8s.aws/instance-size"
          operator: In
          values: ["8xlarge"]
        - key: "karpenter.k8s.aws/instance-hypervisor"
          operator: In
          values: ["nitro"]
        - key: "karpenter.sh/capacity-type"
          operator: In
          values: ["spot"]
        - key: kubernetes.io/arch
          operator: In
          values: ["arm64"]
      expireAfter: Never
  limits:
    cpu: 1000
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 30s
