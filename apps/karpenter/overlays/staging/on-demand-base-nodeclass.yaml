apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: on-demand-base
spec:
  amiFamily: AL2023
  amiSelectorTerms:
    - alias: al2023@latest
  role: Karpenter-staging_eks1
  subnetSelectorTerms:
    - tags:
        Type: private-vpc1
  securityGroupSelectorTerms:
    - tags:
        karpenter.sh/discovery: staging_eks1
  tags:
    karpenter.sh/discovery: staging_eks1
  blockDeviceMappings:
    - deviceName: /dev/xvda
      ebs:
        volumeSize: 100Gi
        volumeType: gp3
        encrypted: true
        deleteOnTermination: true
