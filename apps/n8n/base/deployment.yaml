apiVersion: apps/v1
kind: Deployment
metadata:
  name: n8n
spec:
  replicas: 1
  selector:
    matchLabels:
      app: n8n
  template:
    metadata:
      labels:
        app: n8n
    spec:
      securityContext:
        runAsUser: 1000
        fsGroup: 1000
      serviceAccountName: n8n
      containers:
        - name: n8n
          image: n8nio/n8n
          ports:
            - containerPort: 5678
          volumeMounts:
            - name: n8n-data
              mountPath: /home/<USER>/.n8n
      volumes:
        - name: n8n-data
          persistentVolumeClaim:
            claimName: n8n-data
