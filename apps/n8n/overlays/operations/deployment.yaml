apiVersion: apps/v1
kind: Deployment
metadata:
  name: n8n
spec:
  selector:
    matchLabels:
      app: n8n
  template:
    spec:
      containers:
        - name: n8n
          env:
            - name: N8N_BASIC_AUTH_ACTIVE
              value: "true"
            - name: N8N_BASIC_AUTH_USER
              value: "qro<PERSON><EMAIL>"
            - name: N8N_BASIC_AUTH_PASSWORD
              value: "Password1!"
            - name: N8N_HOST
              value: "n8n.ops.luxurycoders.com"
            - name: N8N_PORT
              value: "5678"
            - name: N8N_PROTOCOL
              value: "http"
            - name: WEBHOOK_URL
              value: "https://n8n.ops.luxurycoders.com/"
            - name: DB_TYPE
              value: "postgresdb"
            - name: DB_POSTGRESDB_DATABASE
              value: "n8n"
            - name: DB_POSTGRESDB_HOST
              value: vault:secret/data/operations/n8n#DB_HOST
            - name: DB_POSTGRESDB_PORT
              value: "5432"
            - name: DB_POSTGRESDB_USER
              value: vault:secret/data/operations/n8n#DB_POSTGRESDB_USER
            - name: DB_POSTGRESDB_PASSWORD
              value: vault:secret/data/operations/n8n#DB_POSTGRESDB_PASSWORD
            - name: DB_POSTGRESDB_SCHEMA
              value: "public"

