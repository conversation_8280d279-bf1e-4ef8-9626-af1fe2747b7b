apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig":
      { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:172158540696:certificate/0ecefba3-1a3b-4282-ae7e-95f527a28685
    alb.ingress.kubernetes.io/group.name: ingress-global-internal
    alb.ingress.kubernetes.io/healthcheck-path: /
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  generation: 1
  labels:
    app.kubernetes.io/instance: n8n
    app.kubernetes.io/name: n8n
  name: n8n
  namespace: n8n
spec:
  rules:
  - host: n8n.ops.luxurycoders.com
    http:
      paths:
      - backend:
          service:
            name: n8n
            port:
              number: 5678
        path: /
        pathType: Prefix
