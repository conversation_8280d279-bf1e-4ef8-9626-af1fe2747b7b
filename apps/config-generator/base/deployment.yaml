apiVersion: apps/v1
kind: Deployment
metadata:
  name: config-generator
  labels:
    app.kubernetes.io/name: config-generator
spec:
  replicas: 2
  selector:
    matchLabels:
      app.kubernetes.io/name: config-generator
      app.kubernetes.io/instance: config-generator
  template:
    metadata:
      labels:
        app.kubernetes.io/name: config-generator
        app.kubernetes.io/instance: config-generator
    spec:
      serviceAccountName: config-generator
      containers:
        - name: config-generator
          image: "luxurypresence/config-generator:hackweek"
          imagePullPolicy: Always
          command: ["node"]
          args: ["programEntrypoint.js", "-u"]
      imagePullSecrets:
        - name: image-pull-secret
