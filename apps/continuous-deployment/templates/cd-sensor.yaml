apiVersion: argoproj.io/v1alpha1
kind: Sensor
metadata:
  name: {{ .Values.argoAppName }}-cd-sensor
spec:
  template:
    serviceAccountName: operate-workflow-sa
    container:
      resources:
        requests:
          cpu: "10m"
          memory: "20Mi"
        limits:
          cpu: "30m"
          memory: "60Mi"
  dependencies:
    - name: docker-registry-event
      eventSourceName: webhook-eventsource
      eventName: image-push
      filters:
        script: |-
          function match_semver(string)
              local string = string:match('^v%d+.%d+.%d+$')
              if string then
                return true
              else
                return false
              end
          end

          function match_prefix(string)
              local string = string:match('^{{ .Values.tagPrefixPattern }}')
              if string then
                return true
              else
                return false
              end
          end

          if event.body.repository.repo_name ~= "{{ .Values.imageRepositoryName }}"
          then
            return false
          end

          if "{{ .Values.semverPolicyEnabled }}" == "is-semver-true" then
            return match_semver(event.body.push_data.tag)
          else
            return match_prefix(event.body.push_data.tag)
          end
  triggers:
    - template:
        name: webhook-workflow-trigger
        k8s:
          operation: create
          source:
            resource:
              apiVersion: argoproj.io/v1alpha1
              kind: Workflow
              metadata:
                generateName: deployment-
              spec:
                arguments:
                  parameters:
                    - name: image-tag
                      value: 'REPLACE ME'
                    - name: argo-app-name
                      value: {{ .Values.argoAppName }}
                    - name: app-name
                      value: {{ .Values.appName }}
                    - name: repository-name
                      value: "{{ .Values.imageRepositoryName }}"
                    - name: migration-repository-name
                      value: "{{ .Values.migrationImageRepositoryName | trimPrefix "migration-image-" }}"
                    - name: env
                      value: {{ .Values.env }}
                workflowTemplateRef:
                  name: rollout-workflow-template
          parameters:
            - src:
                dependencyName: docker-registry-event
                dataKey: body.push_data.tag
              dest: spec.arguments.parameters.0.value
