## How to work with Lua script?
1. Go here https://www.lua.org/cgi-bin/demo
2. Used below script for debugging filter expression
```
local event = {
    body = {
      push_data = {
        tag = "master-123-sdfsfsf"
      },
      repository = {
        name = "foo"
      }
    }
}

function test ()
  if event.body.repository.name == "foo" then return true else return false end
end


local result = test()

io.write("Result is: ", tostring(result))
```
