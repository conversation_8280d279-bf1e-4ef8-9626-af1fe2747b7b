apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  namespace: networking
  name: global-internal-ingress
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:093949242303:certificate/5c403984-be4a-40cd-8e94-62eb4924163f
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/group.name: global-internal-ingress
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/subnets: subnet-032ed3838db0dd253, subnet-06b7b9a9b45e031c4, subnet-054fa6963eec03a46
    alb.ingress.kubernetes.io/target-type: ip
spec:
  ingressClassName: alb
  rules:
    - host: gb-internal-data.staging.luxurycoders.com
      http:
        paths:
          - path: /*
            pathType: ImplementationSpecific
            backend:
              service:
                name: global-internal-service
                port:
                  number: 80
