apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  namespace: networking
  name: global-ingress
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:093949242303:certificate/5c403984-be4a-40cd-8e94-62eb4924163f
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/group.name: global-ingress
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/subnets: subnet-0d434d7cdfef3f698, subnet-0c20c8c609d4eebe8, subnet-057dcde69519483db
    alb.ingress.kubernetes.io/target-type: ip
spec:
  ingressClassName: alb
  rules:
    - host: gb-data.staging.luxurycoders.com
      http:
        paths:
          - path: /*
            pathType: ImplementationSpecific
            backend:
              service:
                name: global-service
                port:
                  number: 80
