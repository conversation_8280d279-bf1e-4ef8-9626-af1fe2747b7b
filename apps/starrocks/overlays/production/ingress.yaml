apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: starrocks-ingress
  namespace: starrocks
  annotations:
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/group.name: ingress-global-internal
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/healthcheck-path: /ready
    kubernetes.io/ingress.class: alb
spec:
  ingressClassName: alb
  rules:
    - host: starrocks.luxurypresence.com
      http:
        paths:
        - path: /*
          pathType: ImplementationSpecific
          backend:
            service:
              name: kube-starrocks-fe-service
              port:
                number: 8030 # Default StarRocks FE HTTP port
    - host: starrocks-backend.luxurypresence.com
      http:
        paths:
        - path: /*
          pathType: ImplementationSpecific
          backend:
            service:
              name: kube-starrocks-be-service
              port:
                number: 8040
    - host: starrocks-compute.luxurypresence.com
      http:
        paths:
        - path: /*
          pathType: ImplementationSpecific
          backend:
            service:
              name: kube-starrocks-cn-service
              port:
                number: 8040
