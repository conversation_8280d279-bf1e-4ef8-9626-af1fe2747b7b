apiVersion: batch/v1
kind: Job
metadata:
  name: {{ .Values.configFileName }}-create-job
spec:
  template:
    spec:
      serviceAccountName: {{ .Values.serviceAccountName }}
      initContainers:
        - name: download-env
          image: amazon/aws-cli:2.2.9
          imagePullPolicy: IfNotPresent
          command: [ "aws", "s3", "cp", "{{ .Values.s3URL }}", "/config/{{ .Values.fileName }}" ]
          volumeMounts:
            - mountPath: /config
              name: app-config
      containers:
        - name: init-config
          image: bitnamilegacy/kubectl
          command: [ "/bin/sh","-c" ]
          args: ["kubectl create configmap {{ .Values.configFileName }} --from-file=/config/{{ .Values.fileName }} --dry-run=client -o yaml | kubectl apply -f -"]
          volumeMounts:
            - mountPath: /config/{{ .Values.fileName }}
              name: app-config
              subPath: {{ .Values.fileName }}
      volumes:
        - emptyDir: { }
          name: app-config

      restartPolicy: Never
  backoffLimit: 0
