apiVersion: batch/v1
kind: Job
metadata:
  name: propagate-config-argo-noti3
spec:
  template:
    spec:
      serviceAccountName: argo-noti-prepare-config-sa
      containers:
        - name: propagate-config
          image: bitnamilegacy/kubectl:1.21.3
          workingDir: /app
          command: ["/bin/sh", "-e","-c"]
          args:
            - >-
              TOKEN=$(kubectl --namespace qa get configmap cluster-info -o jsonpath='{.data.SLACK_APP_ACCESS_TOKEN}');
              kubectl create secret generic argocd-notifications-secret --from-literal=slack-token=$TOKEN --dry-run=client -o yaml | kubectl apply -f -;
      restartPolicy: OnFailure
      imagePullSecrets:
        - name: image-pull-secret
