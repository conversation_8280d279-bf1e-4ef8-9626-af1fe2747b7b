apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: create-secret-argo-noti-role
rules:
  - apiGroups: [""]
    resources:
      - secrets
    verbs:
      - create
      - delete
      - get
      - patch
      - update
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: create-secret-argo-noti-rb
subjects:
  - kind: ServiceAccount
    name: argo-noti-prepare-config-sa
roleRef:
  kind: Role
  name: create-secret-argo-noti-role
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: get-configmap-argo-noti-cluster-role
rules:
  - apiGroups: [""]
    resources:
      - configmaps
    verbs:
      - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: get-configmap-argo-noti-crb
subjects:
  - kind: ServiceAccount
    name: argo-noti-prepare-config-sa
    namespace: argocd
roleRef:
  kind: ClusterRole
  name: get-configmap-argo-noti-cluster-role
  apiGroup: rbac.authorization.k8s.io
