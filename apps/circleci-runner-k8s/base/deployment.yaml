apiVersion: apps/v1
kind: Deployment
metadata:
  name: circleci-runner-container-agent
  namespace: "circleci"
spec:
  replicas: 4
  selector:
    matchLabels:
      app: container-agent
  template:
    metadata:
      labels:
        app: container-agent
      annotations:
        checksum/config: 89b7a2ed0fe97f389ca4a877f2bc8b4b9a10624ff72589c98663fd8a03c539bd
    spec:
      serviceAccountName: circleci-runner-container-agent
      terminationGracePeriodSeconds: 18300
      volumes:
        - name: taskpod-config
          configMap:
            name: circleci-runner-container-agent
      containers:
        - name: container-agent
          image: "circleci/runner-agent:kubernetes-3"
          imagePullPolicy: Always
          volumeMounts:
            - name: taskpod-config
              mountPath: /etc/container-agent
          env:
            - name: RUNNER_API
              value: https://runner.circleci.com
            - name: AGENT_NAME
              value: circleci-runner-container-agent
            - name: MAX_RUN_TIME
              value: 5h
            - name: MAX_CONCURRENT_TASKS
              value: "20"
            - name: CHECK_ENABLED
              value: "false"
            - name: CHECK_THRESHOLD
              value: "3"
            - name: CHECK_INTERVAL
              value: "15m"
            - name: KUBE_NAMESPACE
              value: "circleci"
            - name: KUBE_TASK_POD_CONFIG
              value: /etc/container-agent/taskpods
            - name: KUBE_TOKEN_SECRETS
              value: circleci-runner-container-agent
            - name: KUBE_LOGGING_IMAGE
              value: "circleci/logging-collector:3"
            - name: KUBE_LOGGING_SECRET
              value: "logging-collector-token"
            - name: KUBE_AUTODETECT_PLATFORM
              value: "true"
            # Agent logging settings
            - name: O11Y_LEVEL
              value: "info"
            - name: O11Y_FORMAT
              value: "json"
            # GC configuration settings
            - name: KUBE_GC_ENABLED
              value: "true"
            - name: KUBE_GC_THRESHOLD
              value: "5h5m"
            - name: KUBE_GC_INTERVAL
              value: "3m" # if .Values.agent.ssh.enabled
          resources:
            limits:
              cpu: "500m"
              memory: "200Mi"
            requests:
              cpu: "100m"
              memory: "200Mi"
          livenessProbe:
            failureThreshold: 5
            httpGet:
              path: /live
              port: 7623
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /ready
              port: 7623
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
