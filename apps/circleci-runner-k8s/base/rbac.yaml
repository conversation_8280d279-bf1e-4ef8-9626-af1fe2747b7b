apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: "circleci-runner-container-agent"
  namespace: "circleci"
rules:
  - apiGroups: [""]
    resources: ["nodes"]
    verbs: ["get", "list"] # if $clusterRole.rules
---
# Source: container-agent/templates/clusterrole.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: "circleci-runner-container-agent"
  namespace: "circleci"
subjects:
  - kind: ServiceAccount
    name: circleci-runner-container-agent
    namespace: "circleci"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: "circleci-runner-container-agent" # if .Values.agent.autodetectPlatform
---
# Source: container-agent/templates/role.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: "circleci-runner-container-agent:"
  namespace: "circleci"
rules:
  - apiGroups: [""]
    resources: ["pods", "pods/exec", "pods/log"]
    verbs: ["get", "watch", "list", "create", "delete"]
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["get", "list", "create", "delete"]
  - apiGroups: ["", "events.k8s.io/v1"]
    resources: ["events"]
    verbs: ["watch"] # if .Values.agent.ssh.enabled # if $role.rules
---
# Source: container-agent/templates/role.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: circleci-runner-container-agent:logging-collector
  namespace: "circleci"
rules:
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["watch"]
  - apiGroups: [""]
    resources: ["pods/log"]
    verbs: ["get"]
---
# Source: container-agent/templates/role.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: "circleci-runner-container-agent:"
  namespace: "circleci"
subjects:
  - kind: ServiceAccount
    name: circleci-runner-container-agent
    namespace: "circleci"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: "circleci-runner-container-agent:" # if .Values.rbac.create
---
# Source: container-agent/templates/role.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: circleci-runner-container-agent:logging-collector
  namespace: "circleci"
subjects:
  - kind: ServiceAccount
    name: logging-collector
    namespace: "circleci"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: circleci-runner-container-agent:logging-collector # if .Values.logging.rbac.create
