apiVersion: v1
kind: Secret
metadata:
  name: circleci-runner-container-agent
  namespace: "circleci"
data:
  luxurypresence.eks-staging: ************************************************************************************************************
  luxurypresence.mobile: ZDI2ZWY0YzQ4MGM4ZTY4OGJkOWU3NDA2MDIxNjIzZWMyYzkxMzUwMWM4NDg2MzFjMzFlMTY0NTBmMzNjMzUzODczMGQxMDIyZDRkNDExNjE=
---
apiVersion: v1
kind: Secret
metadata:
  name: circleci-runner-container-agent-token
  annotations:
    kubernetes.io/service-account.name: circleci-runner-container-agent
type: kubernetes.io/service-account-token
