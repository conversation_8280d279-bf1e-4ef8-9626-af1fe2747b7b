apiVersion: v1
kind: ConfigMap
metadata:
  name: circleci-runner-container-agent
  namespace: "circleci"
data:
  taskpods: |
    resourceClasses:
      luxurypresence.eks-staging:
        metadata:
          annotations:
            karpenter.sh/do-not-disrupt: "true"
        spec:
          serviceAccountName: circleci-runner-container-agent
          containers:
            - resources:
                limits:
                  cpu: 4000m
                  memory: 3Gi
                  ephemeral-storage: 10Gi
                requests:
                  cpu: 2000m
                  memory: 3Gi
                  ephemeral-storage: 10Gi
              livenessProbe:
                initialDelaySeconds: 300
                periodSeconds: 60
                timeoutSeconds: 30
                successThreshold: 1
                failureThreshold: 5
      luxurypresence.mobile:
        metadata:
          annotations:
            karpenter.sh/do-not-disrupt: "true"
        spec:
          serviceAccountName: circleci-runner-container-agent
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                      - key: karpenter.sh/nodepool
                        operator: In
                        values:
                          - on-demand-base
          tolerations:
            - key: node-type
              operator: Equal
              value: on-demand
          containers:
            - resources:
                limits:
                  cpu: 13
                  memory: 30Gi
                  ephemeral-storage: 20Gi
                requests:
                  cpu: 13
                  memory: 30Gi
                  ephemeral-storage: 20Gi
              livenessProbe:
                initialDelaySeconds: 300
                periodSeconds: 60
                timeoutSeconds: 30
                successThreshold: 1
                failureThreshold: 5
