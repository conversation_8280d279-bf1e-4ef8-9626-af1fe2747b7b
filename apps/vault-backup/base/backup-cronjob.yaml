apiVersion: batch/v1
kind: CronJob
metadata:
  name: vault-backup
  annotations:
    vault.security.banzaicloud.io/vault-role: vault-backup
spec:
  schedule: "0 22 * * *"
  jobTemplate:
    spec:
      backoffLimit: 3
      template:
        metadata:
          annotations:
            vault.security.banzaicloud.io/vault-role: vault-backup
        spec:
          serviceAccountName: vault-backup
          volumes:
          - name: shared
            emptyDir: {}
          initContainers:
          - name: raft-snapshot
            image: hashicorp/vault
            imagePullPolicy: IfNotPresent
            env:
            - name: VAULT_TOKEN
              value: vault:login
            command:
            - /bin/sh
            args:
            - -ec
            - |
              echo "Starting raft snapshot";
              vault operator raft snapshot save /shared/vault-raft.snap;
            volumeMounts:
            - mountPath: /shared
              name: shared
          - name: secrets-snapshot
            image: bitnamilegacy/kubectl:1.26
            imagePullPolicy: IfNotPresent
            env:
            - name: SECRET_NAME
              value: vault-unseal-keys
            - name: NAMESPACE
              value: vault
            command:
            - /bin/sh
            args:
            - -ec
            - |
              echo "Starting secrets snapshot";
              kubectl get secret $SECRET_NAME -n $NAMESPACE -o yaml > /shared/secret-$SECRET_NAME.yaml;
            volumeMounts:
            - mountPath: /shared
              name: shared
          containers:
          - name: upload
            image: amazon/aws-cli:2.15.13
            imagePullPolicy: IfNotPresent
            command:
            - /bin/sh
            env:
            - name: BUCKET_NAME
              value: to-be-replaced
            args:
            - -ec
            - |
              echo "Installing dependencies";
              yum install -y tar gzip 2>&1 > /dev/null;
              echo "Compressing and uploading backup";
              tar -czf backup.tar.gz -C /shared/ .;
              aws s3 cp backup.tar.gz s3://$BUCKET_NAME/backups/$(date +"%Y%m%d_%H%M%S")/;
              echo "Backup complete"
            volumeMounts:
            - mountPath: /shared
              name: shared
          restartPolicy: OnFailure
