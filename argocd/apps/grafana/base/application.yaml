apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: grafana
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "31"
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  source:
    repoURL: https://grafana.github.io/helm-charts
    targetRevision: 9.2.6
    chart: grafana
    helm:
      values: |
        grafana.ini:
          server:
            domain: grafana.ops.luxurycoders.com
            root_url: "https://grafana.ops.luxurycoders.com/"
          auth.google:
            enabled: true
            scopes: https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/userinfo.email
            auth_url: https://accounts.google.com/o/oauth2/auth
            token_url: https://accounts.google.com/o/oauth2/token
            allowed_domains: luxurypresence.com
            allow_sign_up: true
        ingress:
          enabled: true
          annotations:
            kubernetes.io/ingress.class: alb
            alb.ingress.kubernetes.io/scheme: internal
            alb.ingress.kubernetes.io/target-type: ip
            alb.ingress.kubernetes.io/group.name: ingress-global-internal
            alb.ingress.kubernetes.io/healthcheck-path: /api/health
            alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
            alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:************:certificate/0ecefba3-1a3b-4282-ae7e-95f527a28685
            alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
          hosts:
            - "grafana.ops.luxurycoders.com"
        plugins:
          - victoriametrics-metrics-datasource
        datasources:
          datasources.yaml:
            apiVersion: 1
            datasources:
            - name: Prometheus
              type: prometheus
              url: http://prometheus-stack-prometheus.monitoring:9090/
              access: proxy
              isDefault: true
            - name: VictoriaMetricsStaging
              type: prometheus
              url: http://vmsingle.staging.luxurycoders.com:80
              access: proxy
              isDefault: false
            - name: VictoriaMetricsProduction
              type: prometheus
              url: http://vmsingle-production.luxurycoders.com:80
              access: proxy
              isDefault: false
        podAnnotations:
          vault.security.banzaicloud.io/vault-role: "global-role"
          cluster-autoscaler.kubernetes.io/safe-to-evict: "false"
          karpenter.sh/do-not-disrupt: "true"
        replicas: 1
        podDisruptionBudget:
          minAvailable: 1
          maxUnavailable: ""
        env:
          GF_SECURITY_ADMIN_USER: "vault:secret/data/operations/global/grafana#ADMIN_USER"
          GF_SECURITY_ADMIN_PASSWORD: "vault:secret/data/operations/global/grafana#ADMIN_PASSWORD"
          GF_AUTH_GOOGLE_CLIENT_SECRET: "vault:secret/data/operations/global/grafana#GOOGLE_CLIENT_SECRET"
          GF_AUTH_GOOGLE_CLIENT_ID: "vault:secret/data/operations/global/grafana#GOOGLE_CLIENT_ID"
        persistence:
          enabled: true
          type: pvc
          storageClassName: gp3
          accessModes:
            - ReadWriteOnce
          size: 10Gi
          finalizers:
            - kubernetes.io/pvc-protection
        useStatefulSet: true
        resources:
          limits:
            cpu: 1000m
            memory: 2048Mi
          requests:
            cpu: 500m
            memory: 2048Mi
        dashboardProviders:
          dashboardproviders.yaml:
            apiVersion: 1
            providers:
            - name: 'prometheus-overview'
              orgId: 1
              type: file
              disableDeletion: false
              editable: true
              options:
                path: /var/lib/grafana/dashboards/prometheus-overview
            - name: 'kubernetes-cluster'
              orgId: 1
              type: file
              disableDeletion: false
              editable: true
              options:
                path: /var/lib/grafana/dashboards/kubernetes-cluster
            - name: 'kubernetes-deployments'
              orgId: 1
              type: file
              disableDeletion: false
              editable: true
              options:
                path: /var/lib/grafana/dashboards/kubernetes-deployments
            - name: 'kubernetes-nodes'
              orgId: 1
              type: file
              disableDeletion: false
              editable: true
              options:
                path: /var/lib/grafana/dashboards/kubernetes-nodes
            - name: 'kubernetes-pods'
              orgId: 1
              type: file
              disableDeletion: false
              editable: true
              options:
                path: /var/lib/grafana/dashboards/kubernetes-pods
        dashboards:
          prometheus-overview:
            prometheus-overview:
              gnetId: 3662
              revision: 2
              datasource: Prometheus
              name: 'Prometheus Overview'
              orgId: 1
              version: 1
              uri: https://grafana.com/api/dashboards/3662/revisions/2/download
          kubernetes-cluster:
            kubernetes-cluster:
              gnetId: 12740
              revision: 1
              datasource: Prometheus
              name: 'Kubernetes Cluster'
              orgId: 1
              version: 1
              uri: https://grafana.com/api/dashboards/12740/revisions/1/download
          kubernetes-deployments:
            kubernetes-deployments:
              gnetId: 8588
              revision: 1
              datasource: Prometheus
              name: 'Kubernetes Deployments'
              orgId: 1
              version: 1
              uri: https://grafana.com/api/dashboards/8588/revisions/1/download
          kubernetes-nodes:
            kubernetes-nodes:
              gnetId: 8171
              revision: 1
              datasource: Prometheus
              name: 'Kubernetes Nodes'
              orgId: 1
              version: 1
              uri: https://grafana.com/api/dashboards/8171/revisions/1/download
          kubernetes-pods:
            kubernetes-pods:
              gnetId: 747
              revision: 1
              datasource: Prometheus
              name: 'Kubernetes Pods'
              orgId: 1
              version: 1
              uri: https://grafana.com/api/dashboards/747/revisions/2/download
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
    - CreateNamespace=true
  destination:
    name: in-cluster
    namespace: grafana
  project: default
