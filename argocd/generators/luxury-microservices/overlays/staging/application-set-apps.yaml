apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: microservices-apps
spec:
  generators:
    - git:
        repoURL: https://github.com/luxurypresence/apps-k8s-config
        revision: HEAD
        files:
          - path: "apps/**/overlays/staging/microservice-config.json"
  template:
    metadata:
      name: '{{path[1]}}'
      annotations:
        notifications.argoproj.io/subscribe.on-deployed-webhook.release-notifications: ""
        notifications.argoproj.io/subscribe.on-health-degraded-webhook.release-notifications: ""
        notifications.argoproj.io/subscribe.on-sync-failed-webhook.release-notifications: ""
        environment: staging
        servicename: "{{serviceName}}"
    spec:
      destination:
        name: in-cluster
        namespace: apps
      source:
        path: 'apps/{{path[1]}}/overlays/staging'
