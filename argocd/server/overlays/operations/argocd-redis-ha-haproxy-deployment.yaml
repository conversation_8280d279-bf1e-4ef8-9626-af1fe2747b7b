apiVersion: apps/v1
kind: Deployment
metadata:
  name: argocd-redis-ha-haproxy
spec:
  template:
    spec:
      tolerations:
        - key: "node-group-name"
          operator: "Equal"
          value: "argocd"
          effect: "NoSchedule"
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: node-group-name
                operator: In
                values:
                - argocd
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchLabels:
                app.kubernetes.io/name: argocd-redis-ha-haproxy
            topologyKey: kubernetes.io/hostname
