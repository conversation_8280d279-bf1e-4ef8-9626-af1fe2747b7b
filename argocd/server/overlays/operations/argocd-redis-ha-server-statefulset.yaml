apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: argocd-redis-ha-server
spec:
  template:
    spec:
      tolerations:
        - key: "node-group-name"
          operator: "Equal"
          value: "argocd"
          effect: "NoSchedule"
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: node-group-name
                operator: In
                values:
                - argocd
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchLabels:
                app.kubernetes.io/name: argocd-redis-ha
            topologyKey: kubernetes.io/hostname
