apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-notifications-cm
data:
  context: |
    argocdUrl: "https://argocd.staging.luxurycoders.com"
  service.slack: |
    token: $slack-token
  service.webhook.release-notifications: |
    url: https://release-notifications.ops.luxurycoders.com
    headers:
      - name: Content-Type
        value: application/json
  template.app-deployed-webhook: |
    webhook:
      release-notifications:
        path: /api/v1/messages/argocd-deployment-succeeded
        method: POST
        body: |
          {
            "revision": {{toJson .app.status.sync.revision}},
            "kind": "argocd:release_deployment_succeeded",
            "environment": "staging",
            "app_name": {{toJson .app.metadata.name}}
          }
  template.app-failed-webhook: |
    webhook:
      release-notifications:
        path: /api/v1/messages/argocd-deployment-failed
        method: POST
        body: |
          {
            "revision": {{toJson .app.status.sync.revision}},
            "kind": "argocd:release_deployment_failed",
            "environment": "staging",
            "app_name": {{toJson .app.metadata.name}},
            "status": {{toJson .app.status}}
          }
  template.app-deployed: |
    email:
      subject: Deployment of {{.app.metadata.name}} to {{.app.metadata.annotations.environment}} completed successfully
    message: |
      :white_check_mark: Deployment of *{{.app.metadata.name}}* to *{{.app.metadata.annotations.environment}}* completed successfully :rocket:
    slack:
      attachments: |
        [
        {
            "color": "#18be52",
            "blocks": [
              {
                "type": "section",
                "fields": [
                  {
                    "type": "mrkdwn",
                    "text": "*Service:* {{.app.metadata.annotations.servicename}}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Environment:* {{.app.metadata.annotations.environment}}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Sync Status:* {{.app.status.sync.status}}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Version:* <{{.app.spec.source.repoURL}}/commit/{{.app.status.sync.revision}}|view commit>"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Docker Images:* {{range $index, $image := .app.status.summary.images}}{{ $repositorySplitted := splitList "/" $image }}{{ $repository := index $repositorySplitted 0 }}{{ if eq $repository "luxurypresence" }}{{ $imageSplitted := splitList ":" $image }}{{ $imageName := index $imageSplitted 0 }}{{ if ne $imageName "luxurypresence/config-generator" }} \n- `{{$image}}`{{end}}{{end}}{{end}}"
                  }
                ]
              },
              {
                "type": "section",
                "fields": [
                  {
                    "type": "mrkdwn",
                    "text": "*Monitoring:* <{{.context.argocdUrl}}/applications/{{.app.metadata.name}}|ArgoCD> | <https://app.datadoghq.com/apm/services/{{.app.metadata.annotations.servicename}}/operations/express.request/resources?env={{.app.metadata.annotations.environment}}|DD APM> | <https://app.datadoghq.com/logs?query=service%3A{{.app.metadata.annotations.servicename}}%20env%3A{{.app.metadata.annotations.environment}}|DD logs>"
                  }
                ]
              }
            ]
          }
        ]
  template.app-health-degraded: |
    email:
      subject: Application {{.app.metadata.name}} on {{.app.metadata.annotations.environment}} has degraded
    message: |
      {{if eq .serviceType "slack"}}:exclamation:{{end}} Application {{.app.metadata.name}} on {{.app.metadata.annotations.environment}} has degraded.
      Application details: {{.context.argocdUrl}}/applications/{{.app.metadata.name}}.
    slack:
      attachments: |-
        [{
          "title": "{{ .app.metadata.name}}",
          "title_link": "{{.context.argocdUrl}}/applications/{{.app.metadata.name}}",
          "color": "#f4c030",
          "fields": [
          {
            "title": "Sync Status",
            "value": "{{.app.status.sync.status}}",
            "short": true
          },
          {
            "title": "Environment",
            "value": "{{.app.metadata.annotations.environment}}",
            "short": true
          }
          {{range $index, $c := .app.status.conditions}}
          {{if not $index}},{{end}}
          {{if $index}},{{end}}
          {
            "title": "{{$c.type}}",
            "value": "{{$c.message}}",
            "short": true
          }
          {{end}}
          ]
        }]
  template.app-sync-failed: |
    email:
      subject: Failed to sync application {{.app.metadata.name}} on {{.app.metadata.annotations.environment}}
    message: |
      {{if eq .serviceType "slack"}}:exclamation:{{end}} The sync operation of application {{.app.metadata.name}} on {{.app.metadata.annotations.environment}} has failed at {{.app.status.operationState.finishedAt}} with the following error: {{.app.status.operationState.message}}
      Sync operation details are available at: {{.context.argocdUrl}}/applications/{{.app.metadata.name}}?operation=true .
    slack:
      attachments: |-
        [{
          "title": "{{ .app.metadata.name}}",
          "title_link":"{{.context.argocdUrl}}/applications/{{.app.metadata.name}}",
          "color": "#E96D76",
          "fields": [
          {
            "title": "Sync Status",
            "value": "{{.app.status.sync.status}}",
            "short": true
          },
          {
            "title": "Environment",
            "value": "{{.app.metadata.annotations.environment}}",
            "short": true
          }
          {{range $index, $c := .app.status.conditions}}
          {{if not $index}},{{end}}
          {{if $index}},{{end}}
          {
            "title": "{{$c.type}}",
            "value": "{{$c.message}}",
            "short": true
          }
          {{end}}
          ]
        }]
  template.app-sync-running: |
    email:
      subject: Start syncing application {{.app.metadata.name}} on {{.app.metadata.annotations.environment}}
    message: |
      The sync operation of application {{.app.metadata.name}} on {{.app.metadata.annotations.environment}} has started at {{.app.status.operationState.startedAt}}.
      Sync operation details are available at: {{.context.argocdUrl}}/applications/{{.app.metadata.name}}?operation=true .
    slack:
      attachments: |-
        [{
          "title": "{{ .app.metadata.name}}",
          "title_link":"{{.context.argocdUrl}}/applications/{{.app.metadata.name}}",
          "color": "#0DADEA",
          "fields": [
          {
            "title": "Sync Status",
            "value": "{{.app.status.sync.status}}",
            "short": true
          },
          {
            "title": "Environment",
            "value": "{{.app.metadata.annotations.environment}}",
            "short": true
          }
          {{range $index, $c := .app.status.conditions}}
          {{if not $index}},{{end}}
          {{if $index}},{{end}}
          {
            "title": "{{$c.type}}",
            "value": "{{$c.message}}",
            "short": true
          }
          {{end}}
          ]
        }]
  template.app-sync-status-unknown: |
    email:
      subject: Application {{.app.metadata.name}} sync status is 'Unknown' on {{.app.metadata.annotations.environment}}
    message: |
      {{if eq .serviceType "slack"}}:exclamation:{{end}} Application {{.app.metadata.name}} sync is 'Unknown' on {{.app.metadata.annotations.environment}}.
      Application details: {{.context.argocdUrl}}/applications/{{.app.metadata.name}}.
      {{if ne .serviceType "slack"}}
      {{range $c := .app.status.conditions}}
          * {{$c.message}}
      {{end}}
      {{end}}
    slack:
      attachments: |-
        [{
          "title": "{{ .app.metadata.name}}",
          "title_link":"{{.context.argocdUrl}}/applications/{{.app.metadata.name}}",
          "color": "#E96D76",
          "fields": [
          {
            "title": "Sync Status",
            "value": "{{.app.status.sync.status}}",
            "short": true
          },
          {
            "title": "Environment",
            "value": "{{.app.metadata.annotations.environment}}",
            "short": true
          }
          {{range $index, $c := .app.status.conditions}}
          {{if not $index}},{{end}}
          {{if $index}},{{end}}
          {
            "title": "{{$c.type}}",
            "value": "{{$c.message}}",
            "short": true
          }
          {{end}}
          ]
        }]
  trigger.on-deployed: |
    - description: Application is synced and healthy. Triggered once per commit.
      oncePer: app.status.operationState.syncResult.revision
      send:
      - app-deployed
      when: app.status.operationState.phase in ['Succeeded'] and app.status.health.status == 'Healthy'
  trigger.on-deployed-webhook: |
    - description: Application is synced and healthy. Triggered once per commit.
      oncePer: app.status.operationState.syncResult.revision
      send:
      - app-deployed-webhook
      when: app.status.operationState.phase in ['Succeeded'] and app.status.health.status == 'Healthy'
  trigger.on-health-degraded: |
    - description: Application has degraded
      send:
      - app-health-degraded
      when: app.status.health.status == 'Degraded'
  trigger.on-health-degraded-webhook: |
    - description: Application has degraded
      send:
      - app-failed-webhook
      when: app.status.health.status == 'Degraded'
  trigger.on-sync-failed: |
    - description: Application syncing has failed
      send:
      - app-sync-failed
      when: app.status.operationState.phase in ['Error', 'Failed']
  trigger.on-sync-failed-webhook: |
    - description: Application syncing has failed
      send:
      - app-failed-webhook
      when: app.status.operationState.phase in ['Error', 'Failed']
  trigger.on-sync-running: |
    - description: Application is being synced
      send:
      - app-sync-running
      when: app.status.operationState.phase in ['Running']
  trigger.on-sync-status-unknown: |
    - description: Application status is 'Unknown'
      send:
      - app-sync-status-unknown
      when: app.status.sync.status == 'Unknown'
  trigger.on-sync-succeeded: |
    - description: Application syncing has succeeded
      send:
      - app-sync-succeeded
      when: app.status.operationState.phase in ['Succeeded']
