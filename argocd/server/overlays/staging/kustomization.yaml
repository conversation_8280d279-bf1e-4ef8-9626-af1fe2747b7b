apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: argocd
resources:
  - https://raw.githubusercontent.com/argoproj/argo-cd/v2.10.3/manifests/ha/install.yaml # Treat this as base
  - external-secrets.yaml
  - auth0-argocd-ss.yaml
  - github-app-creds-ss.yaml
  - repo-creds-ss.yaml
  - ingress_argocd-server.yaml
  - ../../../cluster/staging
  - ../../../cluster/staging-data
  - application.yaml

patches:
  - path: deployment_argocd-applicationset-controller.yaml
  - path: deployment_argocd-dex-server.yaml
  - path: deployment_argocd-notifications-controller.yaml
  - path: deployment_argocd-redis-ha-proxy.yaml
  - path: deployment_argocd-repo-server.yaml
  - path: deployment_argocd-server.yaml
  - path: statefulset_argocd-application-controller.yaml
  - path: statefulset_argocd-redis-ha-server.yaml
  - path: configmap_argocd-cm.yaml
  - path: configmap_argocd-cmd-params-cm.yaml
  - path: configmap_argocd-rbac-cm.yaml
  - path: secret_argocd-secret.yaml
  - path: argocd-notifications-cm.yaml
  - path: argocd-notifications-sa.yaml
